var moment          = require('moment'),
    commonService   = require('../services/commonService'),
    securityService = require('../services/securityService');

let hepatitis = {
    index: function(req, res){
        try {
            const arrPromise = [];
            const errors = [];
            const patient_id = req.params.patient_id;
            const user = req.user;
            let patient = {};
            let detailHepatitis = {};
            let times = [];
            let timeActiveId;
            let typeDetail = {view: 'index'};
            const type = req.params.type;
            if(user.isAdmin || user.role_id.includes(3)){
                // lấy thông tin cơ bản
                arrPromise.push(
                    commonService.getAllDataTable('patients', {id: patient_id}).then(responseData =>{
                        if(responseData.success){
                            if(responseData.data && responseData.data.length > 0){
                                patient = responseData.data[0];
                            }
                        }else{
                            errors.push(responseData.message);  
                        }
                    })
                )
                typeDetail = hepatitis.getTypeDetail(type);
                // <PERSON><PERSON>y thông tin viêm gan
                if(typeDetail.isData){
                    arrPromise.push(
                        hepatitis.getDataHepatitis(patient_id, typeDetail.isTime, typeDetail.table, user).then(responseData =>{
                            detailHepatitis = responseData.detailHepatitis;
                            times = responseData.listTime;
                            timeActiveId = responseData.timeActiveId;
                        })
                    );
                }
            }else{
                errors.push('Bạn không có quyền truy cập bệnh nhân này!');
            }

            Promise.all(arrPromise).then(responseData =>{
                return res.render('viem-gan/' + typeDetail.view, {
                    user: req.user,
                    errors: errors,
                    patient: patient,
                    moment: moment,
                    detailHepatitis: detailHepatitis,
                    times: times,
                    type: type,
                    path: 'viem-gan',
                    timeActiveId: timeActiveId
                });
            })
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack)
            return res.render("error");
        }
    },
    getDataHepatitis: function(patient_id, isTime, table, user = null){
        return new Promise(async (resolve, reject) => {
            try {
                let detailHepatitis = {};
                let listTime = [];
                let condition = {patient_id: patient_id, active: 1};
                let timeActiveId;

                if(isTime){
                    // Get list time
                    switch(table){
                        case 'viem_gan_ttdd': condition['type'] = 'tinh-trang-dinh-duong';
                            break;
                        case 'viem_gan_ctdd': condition['type'] = 'can-thiep-dinh-duong';
                            break;
                        case 'viem_gan_sga': condition['type'] = 'sga';
                            break;
                        default: break;
                    }

                    // Apply role-based filtering for times
                    const timeConditions = securityService.applyRoleBasedFiltering(user, condition);
                    let listTimeResult = await commonService.getAllDataTable('times', timeConditions);
                    if(listTimeResult.success && listTimeResult.data && listTimeResult.data.length > 0){
                        listTime = listTimeResult.data;
                    }
                }

                if(isTime && listTime.length > 0){
                    timeActiveId = listTime[0].id;
                    condition['time_id'] = timeActiveId;
                }
                if(condition.hasOwnProperty('type')) delete condition.type;

                // Apply role-based filtering for main data
                const filteredCondition = securityService.applyRoleBasedFiltering(user, condition);

                // Get detail hepastitis
                commonService.getAllDataTable(table, filteredCondition).then(responseData =>{
                    if(responseData.success){
                        if(responseData.data && responseData.data.length > 0) detailHepatitis = responseData.data[0];
                    }
                    resolve({detailHepatitis: detailHepatitis, listTime: listTime, timeActiveId: timeActiveId});
                })
            } catch (error) {
                // Note: req is not available in this context, need to handle differently
                console.error('Error in getDataHepatitis:', error.message);
                resolve({detailHepatitis: {}, listTime: [], timeActiveId: null})
            }
        })
    },
    getTypeDetail: function(type){
        let view = 'index';
        let table = '';
        let isTime = false;
        let isData = true;
        switch(type){
            case 'dau-hieu-nhap-vien':
                view = 'index';
                table = 'viem_gan_dhnv';
                break;
            case 'thoi-quen-an-uong':
                view = 'thoi-quen-an-uong';
                table = 'viem_gan_tqau';
                break;
            case 'tinh-trang-dinh-duong':
                view = 'tinh-trang-dinh-duong';
                table = 'viem_gan_ttdd';
                isTime = true;
                break;
            case 'can-thiep-dinh-duong':
                view = 'can-thiep-dinh-duong';
                table = 'viem_gan_ctdd';
                isTime = true;
                break;
            case 'sga':
                view = 'sga';
                table = 'viem_gan_sga';
                isTime = true;
                break;
            case 'che-do-an-noi-tru':
                view = 'an-noi-tru';
                table = 'viem_gan_td_not';
                isData = false;
                break;
            case 'che-do-an-ngoai-tru':
                view = 'an-ngoai-tru';
                table = 'viem_gan_td_ngt';
                isData = false;
                break;
            default: break;
        }
        return {view: view, table: table, isTime: isTime, isData: isData};
    },
    getListTable: function(req, res, next){
        try {
            var resultMessage = {
                "data": [],
                "error": "",
                "draw": "1",
                "recordsFiltered": 0,
                "recordsTotal": 0
            };
            var arrPromise = [],
                errors     = [],
                baseParameter  = {
                    skip: isNaN(parseInt(req.body.start)) ? 0 : parseInt(req.body.start),
                    take: isNaN(parseInt(req.body.length)) ? 15 : parseInt(req.body.length),
                    search_value: req.body['search[value]'],
                    table: req.params.type == 'che-do-an-noi-tru' ? 'viem_gan_td_not' : 'viem_gan_td_ngt',
                    patient_id: req.params.patient_id
                };

            // Apply role-based filtering
            const roleBasedConditions = securityService.applyRoleBasedFiltering(req.user, {});
            const parameter = { ...baseParameter, ...roleBasedConditions };

            if(!req.user.role_id.includes(3) && !req.user.isAdmin){
                resultMessage.error = 'Bạn không có quyền truy cập danh sách này!';
                return res.json(resultMessage);
            }
            arrPromise.push(commonService.countAllBoarding(parameter).then(responseData =>{
                if(responseData.success){
                    if(responseData.data && responseData.data.length > 0){
                        let count = responseData.data[0].count;
                        resultMessage.recordsFiltered = count;
                        resultMessage.recordsTotal = count;
                    }
                }else{
                    errors.push(responseData.message);
                }
            }));

            arrPromise.push(commonService.getAllBoarding(parameter).then(responseData =>{
                if(responseData.success){
                    if(responseData.data && responseData.data.length > 0) resultMessage.data = responseData.data;
                }else{
                    errors.push(responseData.message);
                }
            }));
            Promise.all(arrPromise).then(()=>{
                resultMessage.draw = req.body.draw;
                if(errors.length > 0){
                    resultMessage.error = errors.join(', ');
                }
                return res.json(resultMessage);
            })
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json({
                "data": [],
                "error": "Có lỗi xảy ra, vui lòng thử lại sau!",
                "draw": "1",
                "recordsFiltered": 0,
                "recordsTotal": 0
            });
        }
    },
    deleteBroading: function(req, res){
        try {
            var resultData = {
                success: false,
                message: ""
            };
            let id = req.params.id;
            let table = req.params.type == 'che-do-an-noi-tru' ? 'viem_gan_td_not' : 'viem_gan_td_ngt';
            if(!req.user.role_id.includes(3) && !req.user.isAdmin){
                resultMessage.error = 'Bạn không có xóa danh sách này!';
                return res.json(resultMessage);
            }
            if(id){
                commonService.updateRecordTable({active: 0}, {id: id}, table).then(responseData =>{
                    if(responseData.success){
                        resultData.success = true;
                        resultData.message = 'Thành công!';
                    }else{
                        resultData.message = responseData.message;
                    }
                    return res.json(resultData);
                })
            }else{
                resultData.message = 'Thiếu Id bệnh nhân!';
                return res.json(resultData);
            }
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    deleteTime: function(req, res){
        try {
            var resultData = {
                success: false,
                message: ""
            };
            let id = req.params.id;
            let patient_id = req.body.patient_id;
            if(!req.user.role_id.includes(3) && !req.user.isAdmin){
                resultMessage.error = 'Bạn không có quyền xóa danh sách này!';
                return res.json(resultMessage);
            }
            if(id){
                commonService.updateRecordTable({active: 0}, {id: id, patient_id: patient_id}, 'times').then(responseData =>{
                    if(responseData.success){
                        resultData.success = true;
                        resultData.message = 'Thành công!';
                        switch(req.params.type){
                            case 'tinh-trang-dinh-duong':
                                commonService.updateRecordTable({active: 0}, {time_id: id, patient_id: patient_id}, 'viem_gan_ttdd')
                                break;
                            case 'can-thiep-dinh-duong':
                                commonService.updateRecordTable({active: 0}, {time_id: id, patient_id: patient_id}, 'viem_gan_ctdd')
                                break;
                            case 'sga':
                                commonService.updateRecordTable({active: 0}, {time_id: id, patient_id: patient_id}, 'viem_gan_sga')
                                break;
                            default: break;
                        }
                    }else{
                        resultData.message = responseData.message;
                    }
                    return res.json(resultData);
                })
            }else{
                resultData.message = 'Thiếu Id bệnh nhân!';
                return res.json(resultData);
            }
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    editHepatitis: async function(req, res){
        try {
            var resultData = {
                success: false,
                message: "",
                data: ''
            };
            // new Date().toLocaleDateString('fr-CA');
            const validateRules = [
            //     { field: "fullname", type: "string", required: true, message: "Vui lòng nhập họ tên!" },
            //     { field: "ma_benh_an", type: "string", required: true, message: "Vui lòng nhập mã bệnh án!" }
            ];
            const parameter = hepatitis.getDataBodyHepatitis(req.body, req.params.type);
            const errors = securityService.validateInput(parameter.data, validateRules, { returnType: 'array' });
            if(!req.user.role_id.includes(3) && !req.user.isAdmin){
                resultData.message = 'Bạn không có quyền sửa danh sách này!';
                return res.json(resultData);
            }

            // Kiểm tra quyền sở hữu dữ liệu (user thường chỉ sửa được dữ liệu do họ tạo)
            // if(!req.user.isAdmin && parameter.condition.id){
            //     const checkOwnership = await commonService.getAllDataTable(parameter.table, {id: parameter.condition.id});
            //     if(checkOwnership.success && checkOwnership.data && checkOwnership.data.length > 0){
            //         const record = checkOwnership.data[0];
            //         if(record.created_by && record.created_by !== req.user.id){
            //             resultData.message = 'Bạn không có quyền sửa dữ liệu này!';
            //             return res.json(resultData);
            //         }
            //     }else{
            //         resultData.message = 'Không tìm thấy dữ liệu để sửa!';
            //         return res.json(resultData);
            //     }
            // }

            if(errors.length > 0){
                resultData.message = errors.map(s => s.message).join(', ');
                return res.json(resultData);
            }else{
                parameter.data['patient_id'] = req.params.patient_id;
                // Không thay đổi created_by khi sửa
                commonService.updateRecordTable(parameter.data, parameter.condition, parameter.table).then(responseData =>{
                    if(responseData.success && responseData.data){
                        resultData.success = true;
                        resultData.message = 'Lưu thành công!';
                    }else{
                        resultData.message = responseData.message;
                    }
                    res.json(resultData);
                })
            }
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    createHepatitis: function(req, res){
        try {
            var resultData = {
                success: false,
                message: "",
                data: ''
            };
            // new Date().toLocaleDateString('fr-CA');
            const validateRules = [
            //     { field: "fullname", type: "string", required: true, message: "Vui lòng nhập họ tên!" },
            //     { field: "ma_benh_an", type: "string", required: true, message: "Vui lòng nhập mã bệnh án!" }
            ];
            const parameter = hepatitis.getDataBodyHepatitis(req.body, req.params.type);
            const errors = securityService.validateInput(parameter.data, validateRules, { returnType: 'array' });
            if(!req.user.role_id.includes(3) && !req.user.isAdmin){
                resultMessage.error = 'Bạn không có quyền tạo danh sách này!';
                return res.json(resultMessage);
            }
            if(errors.length > 0){
                resultData.message = errors.map(s => s.message).join(', ');
                return res.json(resultData);
            }else{
                parameter.data['patient_id'] = req.params.patient_id;
                parameter.data['created_by'] = req.user.id;
                commonService.addRecordTable(parameter.data, parameter.table, true).then(responseData =>{
                    if(responseData.success && responseData.data){
                        resultData.success = true;
                        resultData.message = 'Lưu thành công!';
                        resultData.data = responseData.data;
                    }else{
                        resultData.message = responseData.message;
                    }
                    res.json(resultData);
                })
            }
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    getDataBodyHepatitis: function(body, type){
        switch(type){
            case 'dau-hieu-nhap-vien':
                return {data: hepatitis.dauHieuNhapVien(body), table: 'viem_gan_dhnv', condition: {id: body.id ? body.id : ''}}
            case 'thoi-quen-an-uong':
                return {data: hepatitis.thoiQuenAnUong(body), table: 'viem_gan_tqau', condition: {id: body.id ? body.id : ''}}
            case 'tinh-trang-dinh-duong':
                return {data: hepatitis.tinhTrangDinhDuong(body), table: 'viem_gan_ttdd', condition: {id: body.id ? body.id : ''}}
            case 'can-thiep-dinh-duong':
                return {data: hepatitis.canThiepDinhDuong(body), table: 'viem_gan_ctdd', condition: {id: body.id ? body.id : ''}}
            case 'sga':
                return {data: hepatitis.sga(body), table: 'viem_gan_sga', condition: {id: body.id ? body.id : ''}}
        }   
    },
    dauHieuNhapVien: function(body){
        return {
            chan_an_met_moi: body.chan_an_met_moi,
            bieu_hien_tieu_hoa: body.bieu_hien_tieu_hoa,
            dau_tuc_hsp: body.dau_tuc_hsp,
            dau_tuc_hsp_khi: body.dau_tuc_hsp_khi,
            vang_da_vang_mat: body.vang_da_vang_mat,
            bieu_hien_phu: body.bieu_hien_phu,
            bieu_hien_co_chuong: body.bieu_hien_co_chuong,
            ngua_da: body.ngua_da,
            ngua_da_khac: body.ngua_da_khac,
            xuat_huyet_tieu_hoa: body.xuat_huyet_tieu_hoa
        }
    },
    thoiQuenAnUong: function(body){
        return {
            bua_chinh: body.bua_chinh,
            bua_phu: body.bua_phu,
            bua_phu_an: body.bua_phu_an,
            bua_phu_an_khac: body.bua_phu_an_khac,
            an_kieng: body.an_kieng,
            an_kieng_loai: body.an_kieng_loai,
            an_kieng_loai_khac: body.an_kieng_loai_khac,
            ruou_bia: body.ruou_bia,
            ruou_bia_ts: body.ruou_bia_ts,
            ml_ruou: body.ml_ruou,
            ml_bia: body.ml_bia,
            do_uong_khac: body.do_uong_khac,
            do_uong_khac_ts: body.do_uong_khac_ts,
            loai_do_uong: body.loai_do_uong,
            loai_do_uong_khac: body.loai_do_uong_khac,
            loai_la_cay: body.loai_la_cay,
            cham_soc_dd: body.cham_soc_dd,
            cham_soc_dd_khac: body.cham_soc_dd_khac
        }
    },
    tinhTrangDinhDuong: function(body){
        return {
            cn: body.cn,
            cc: body.cc,
            vong_bap_chan: body.vong_bap_chan,
            glucose: body.glucose,
            ure: body.ure,
            creatinin: body.creatinin,
            got: body.got,
            gpt: body.gpt,
            ggt: body.ggt,
            hong_cau: body.hong_cau,
            hemoglobin: body.hemoglobin,
            pre_albumin: body.pre_albumin,
            albumin: body.albumin,
            protein_tp: body.protein_tp,
            sat_huyet_thanh: body.sat_huyet_thanh,
            ferritin: body.ferritin,
            time_id: body.time_id
        }
    },
    canThiepDinhDuong: function(body){
        return {
            chan_an: body.chan_an,
            chan_an_note: body.chan_an_note,
            an_khong_ngon: body.an_khong_ngon,
            an_khong_ngon_note: body.an_khong_ngon_note,
            buon_non: body.buon_non,
            buon_non_note: body.buon_non_note,
            non: body.non,
            non_note: body.non_note,
            tao_bon: body.tao_bon,
            tao_bon_note: body.tao_bon_note,
            tieu_chay: body.tieu_chay,
            tieu_chay_note: body.tieu_chay_note,
            song_phan: body.song_phan,
            song_phan_note: body.song_phan_note,
            nhiet_mieng: body.nhiet_mieng,
            nhiet_mieng_note: body.nhiet_mieng_note,
            thay_doi_vi_giac: body.thay_doi_vi_giac,
            thay_doi_vi_giac_note: body.thay_doi_vi_giac_note,
            khac: body.khac,
            khac_note: body.khac_note,
            co_chuong: body.co_chuong,
            co_chuong_note: body.co_chuong_note,
            met_moi: body.met_moi,
            met_moi_note: body.met_moi_note,
            dau: body.dau,
            dau_note: body.dau_note,
            time_id: body.time_id
        }
    },
    sga: function(body){
        return {
            cn_6_thang: body.cn_6_thang,
            cn_2_tuan: body.cn_2_tuan,
            khau_phan_an_ht: body.khau_phan_an_ht,
            tieu_chung_th: body.tieu_chung_th,
            giam_chuc_nang: body.giam_chuc_nang,
            nc_chuyen_hoa: body.nc_chuyen_hoa,
            mo_duoi_da: body.mo_duoi_da,
            teo_co: body.teo_co,
            phu: body.phu,
            co_chuong: body.co_chuong,
            phan_loai: body.phan_loai,
            time_id: body.time_id
        }
    },
    anNoiTru: function(body){
        return {
            time: body.date,
            nd_duong_th: body.nd_duong_th,
            nd_tinh_mac: body.nd_tinh_mac,
            note: body.note
        }
    },
    anNgoaiTru: function(body){
        return {
            time: body.date,
            cn: body.cn,
            bat_thuong: body.bat_thuong,
            tu_van: body.tu_van,
            note: body.note
        }
    },
    addTimes: function(req, res){
        try {
            var resultData = {
                success: false,
                message: "",
                insertId: ''
            };
            const validateRules = [
                { field: "time", type: "string", required: true, message: "Vui lòng chọn ngày!" }
            ];
            const parameter = {
                patient_id: req.params.patient_id,
                time: req.body.time,
                type: req.params.type,
                project:'viem-gan',
                created_by: req.user.id
            };
            const errors = securityService.validateInput(parameter, validateRules, { returnType: 'array' });
            if(!req.user.role_id.includes(3) && !req.user.isAdmin){
                resultMessage.error = 'Bạn không có quyền tạo danh sách này!';
                return res.json(resultMessage);
            }
            if(errors.length > 0){
                resultData.message = errors.map(s => s.message).join(', ');
                return res.json(resultData);
            }else{
                commonService.addRecordTable(parameter, 'times', true).then(responseData =>{
                    if(responseData.success && responseData.data){
                        resultData.success = true;
                        resultData.message = 'Thành công!';
                        resultData.insertId = responseData.data.insertId;
                    }else{
                        resultData.message = responseData.message;
                    }
                    res.json(resultData);
                })
            }
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    updateTimes: function(req, res){
        try {
            var resultData = {
                success: false,
                message: ""
            };
            const validateRules = [
                { field: "time", type: "string", required: true, message: "Vui lòng chọn ngày!" },
                { field: "timeActive", type: "string", required: true, message: "Vui lòng chọn ngày được sửa!" },
            ];
            const parameter = {
                time: req.body.time,
                timeActive: req.body.timeActive
            };
            const errors = securityService.validateInput(parameter, validateRules, { returnType: 'array' });
            if(!req.user.role_id.includes(3) && !req.user.isAdmin){
                resultMessage.error = 'Bạn không có quyền sửa danh sách này!';
                return res.json(resultMessage);
            }
            if(errors.length > 0){
                resultData.message = errors.map(s => s.message).join(', ');
                return res.json(resultData);
            }else{
                delete parameter.timeActive;
                commonService.updateRecordTable(parameter, {id: req.body.timeActive},'times').then(responseData =>{
                    if(responseData.success && responseData.data){
                        resultData.success = true;
                        resultData.message = 'Thành công!';
                    }else{
                        resultData.message = responseData.message;
                    }
                    res.json(resultData);
                })
            }
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    addBroading: function(req, res){
        try {
            var resultData = {
                success: false,
                message: "",
                insertId: ''
            };
            const validateRules = [
                // { field: "time", type: "string", required: true, message: "Vui lòng chọn ngày!" }
            ];
            const parameter = hepatitis.getDataBodyBroading(req.body, req.params.type);
            const errors = securityService.validateInput(parameter.data, validateRules, { returnType: 'array' });
            if(!req.user.role_id.includes(3) && !req.user.isAdmin){
                resultMessage.error = 'Bạn không có quyền tạo danh sách này!';
                return res.json(resultMessage);
            }
            if(errors.length > 0){
                resultData.message = errors.map(s => s.message).join(', ');
                return res.json(resultData);
            }else{
                parameter.data['created_by'] = req.user.id;
                parameter.data['patient_id'] = req.params.patient_id;
                parameter.data.time = parameter.data.time.split("/").reverse().join("/");
                commonService.addRecordTable(parameter.data, parameter.table, true).then(responseData =>{
                    if(responseData.success && responseData.data){
                        resultData.success = true;
                        resultData.message = 'Thành công!';
                        resultData.insertId = responseData.data.insertId;
                    }else{
                        resultData.message = responseData.message;
                    }
                    res.json(resultData);
                })
            }
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    getDataBodyBroading: function(body, type){
        switch(type){
            case 'che-do-an-noi-tru':
                return {data: hepatitis.anNoiTru(body), table: 'viem_gan_td_not'}
            case 'che-do-an-ngoai-tru':
                return {data: hepatitis.anNgoaiTru(body), table: 'viem_gan_td_ngt'}
            default: break;
        }
    },
    updateBroading: function(req, res){
        try {
            var resultData = {
                success: false,
                message: ""
            };
            const validateRules = [
                // { field: "time", type: "string", required: true, message: "Vui lòng chọn ngày!" }
            ];
            const parameter = hepatitis.getDataBodyBroading(req.body, req.params.type);
            const errors = securityService.validateInput(parameter.data, validateRules, { returnType: 'array' });
            let id = req.body.id;
            if(!id){
                errors.push('Thiếu Id');
            }
            if(!req.user.role_id.includes(3) && !req.user.isAdmin){
                resultMessage.error = 'Bạn không có quyền sửa danh sách này!';
                return res.json(resultMessage);
            }
            if(errors.length > 0){
                resultData.message = errors.map(s => s.message).join(', ');
                return res.json(resultData);
            }else{
                parameter.data.time = parameter.data.time.split("/").reverse().join("/");
                commonService.updateRecordTable(parameter.data, {id: req.body.id}, parameter.table).then(responseData =>{
                    if(responseData.success && responseData.data){
                        resultData.success = true;
                        resultData.message = 'Thành công!';
                    }else{
                        resultData.message = responseData.message;
                    }
                    res.json(resultData);
                })
            }
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    dataBroading: function(req, res){
        try {
            var resultData = {
                success: false,
                message: "",
                data: {}
            };
            let table = 'viem_gan_td_not';
            if(req.params.type == 'che-do-an-ngoai-tru') table = 'viem_gan_td_ngt';
            if(!req.user.role_id.includes(3) && !req.user.isAdmin){
                resultData.message = 'Bạn không có quyền truy cập danh sách này!';
                return res.json(resultData);
            }

            // Apply role-based filtering
            const conditions = securityService.applyRoleBasedFiltering(req.user, {id: req.params.id});

            commonService.getAllDataTable(table, conditions).then(responseData =>{
                if(responseData.success){
                    resultData.success = true;
                    if(responseData.data && responseData.data.length > 0){
                        resultData.data = responseData.data[0];
                    }else{
                        resultData.message = 'Không có dữ liệu';
                    }
                }else{
                    resultData.message = responseData.message;
                }
                return res.json(resultData);
            })
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    dataTime:function(req, res){
        try {
            var resultData = {
                success: false,
                message: "",
                data: {}
            };

            let table = '';
            switch(req.params.type){
                case 'tinh-trang-dinh-duong': table = 'viem_gan_ttdd'
                    break;
                case 'can-thiep-dinh-duong': table = 'viem_gan_ctdd'
                    break;
                case 'sga': table = 'viem_gan_sga'
                    break;
                default: break
            }
            if(!req.user.role_id.includes(3) && !req.user.isAdmin){
                resultData.message = 'Bạn không có quyền truy cập danh sách này!';
                return res.json(resultData);
            }

            // Apply role-based filtering
            const conditions = securityService.applyRoleBasedFiltering(req.user, {
                patient_id: req.params.patient_id,
                time_id: req.body.time_id
            });

            commonService.getAllDataTable(table, conditions).then(responseData =>{
                if(responseData.success){
                    resultData.success = true;
                    if(responseData.data && responseData.data.length > 0){
                        resultData.data = responseData.data[0];
                    }else{
                        resultData.message = 'Không có dữ liệu';
                    }
                }else{
                    resultData.message = responseData.message;
                }
                return res.json(resultData);
            })
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
}

module.exports = hepatitis;