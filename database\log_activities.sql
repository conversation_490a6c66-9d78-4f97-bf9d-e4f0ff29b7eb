/*
SQLyog Community
MySQL - 8.0.33-25 
*********************************************************************
*/
/*!40101 SET NAMES utf8 */;

CREATE TABLE `log_activities` (
	`id` BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
	`user_id` BIGINT,
	`name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
	`message` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
	`full_message` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
	`url` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
	`method` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
	`ip` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
	`agent` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
	`form_data` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
	`created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	`updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
