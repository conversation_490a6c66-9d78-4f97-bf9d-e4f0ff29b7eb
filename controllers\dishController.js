const commonService = require('../services/commonService'),
    securityService = require('../services/securityService');

const dishController = {
    // Hiển thị danh sách món ăn
    list: (req, res) => {
        try {
            const errors = [];
            res.render('admin/mon-an/list', {
                user: req.user,
                errors: errors
            });
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            return res.render("error");
        }
    },
    
    // Hiển thị form tạo/sửa món ăn
    detail: async (req, res) => {
        const errors = [];
        const user = req.user;
        const id = req.params.id;
        let dishData = null;
        let dishFoods = [];
        
        try {
            if (id && id !== 'new') {
                // Lấy thông tin món ăn hiện tại
                const currentDishRes = await commonService.getAllDataTable('dishes', { id: id });
                if (currentDishRes.success && currentDishRes.data && currentDishRes.data.length > 0) {
                    dishData = currentDishRes.data[0];
                    
                    // L<PERSON>y danh sách thực phẩm trong món ăn
                    const dishFoodsRes = await commonService.getListTable(`
                        SELECT 
                            df.*,
                            fi.name as food_name,
                            fi.code as food_code,
                            fi.type as food_type,
                            fi.type_year as food_type_year,
                            fi.ten as food_ten,
                            fi.edible as food_edible,
                            fi.protein as food_protein,
                            fi.animal_protein as food_animal_protein,
                            fi.unanimal_lipid as food_unanimal_lipid,
                            mn.energy as food_energy,
                            mn.water as food_water,
                            mn.fat as food_fat,
                            mn.carbohydrate as food_carbohydrate,
                            mn.fiber as food_fiber,
                            mn.ash as food_ash,
                            mn.calci as food_calci,
                            mn.phosphorous as food_phosphorous,
                            mn.fe as food_fe,
                            mn.zinc as food_zinc,
                            mn.sodium as food_sodium,
                            mn.potassium as food_potassium,
                            mn.magnesium as food_magnesium,
                            mn.manganese as food_manganese,
                            mn.copper as food_copper,
                            mn.selenium as food_selenium
                        FROM dish_foods df
                        LEFT JOIN food_info fi ON df.food_id = fi.id
                        LEFT JOIN main_nutrients mn ON fi.id = mn.id_food
                        WHERE df.dish_id = ?
                        ORDER BY df.order_index ASC, df.id ASC
                    `, [id]);
                    
                    if (dishFoodsRes.success && dishFoodsRes.data) {
                        // Tính toán dinh dưỡng theo khối lượng thực tế
                        dishFoods = dishFoodsRes.data.map(food => {
                            const ratio = food.weight / 100; // food_info lưu theo 100g
                            return {
                                ...food,
                                calculated_energy: (parseFloat(food.food_energy) || 0) * ratio,
                                calculated_protein: (parseFloat(food.food_protein) || 0) * ratio,
                                calculated_fat: (parseFloat(food.food_fat) || 0) * ratio,
                                calculated_carbohydrate: (parseFloat(food.food_carbohydrate) || 0) * ratio,
                                calculated_animal_protein: (parseFloat(food.food_animal_protein) || 0) * ratio,
                                calculated_unanimal_lipid: (parseFloat(food.food_unanimal_lipid) || 0) * ratio,
                                calculated_water: (parseFloat(food.food_water) || 0) * ratio,
                                calculated_fiber: (parseFloat(food.food_fiber) || 0) * ratio,
                                calculated_ash: (parseFloat(food.food_ash) || 0) * ratio,
                                calculated_calci: (parseFloat(food.food_calci) || 0) * ratio,
                                calculated_phosphorous: (parseFloat(food.food_phosphorous) || 0) * ratio,
                                calculated_fe: (parseFloat(food.food_fe) || 0) * ratio,
                                calculated_zinc: (parseFloat(food.food_zinc) || 0) * ratio,
                                calculated_sodium: (parseFloat(food.food_sodium) || 0) * ratio,
                                calculated_potassium: (parseFloat(food.food_potassium) || 0) * ratio,
                                calculated_magnesium: (parseFloat(food.food_magnesium) || 0) * ratio,
                                calculated_manganese: (parseFloat(food.food_manganese) || 0) * ratio,
                                calculated_copper: (parseFloat(food.food_copper) || 0) * ratio,
                                calculated_selenium: (parseFloat(food.food_selenium) || 0) * ratio
                            };
                        });
                    }
                }
            }
            
        } catch (error) {
            errors.push('Có lỗi xảy ra khi tải dữ liệu: ' + error.message);
            commonService.saveLog(req, error.message, error.stack);
        }
        
        res.render('admin/mon-an/index', {
            user: user,
            errors: errors,
            dishData: dishData,
            dishFoods: dishFoods,
            isEdit: id && id !== 'new'
        });
    },
    
    // API lấy danh sách món ăn cho DataTable
    listData: (req, res) => {
        try {
            // Kiểm tra quyền truy cập
            if (!req.user.isAdmin) {
                return res.json({
                    draw: req.body.draw || 1,
                    recordsTotal: 0,
                    recordsFiltered: 0,
                    data: [],
                    error: 'Bạn không có quyền truy cập danh sách này!'
                });
            }
            
            const parameter = {
                table: 'dishes',
                columns: ['id', 'name', 'description', 'category', 'created_at'],
                primaryKey: 'id',
                active: -1,
                activeOperator: '!=',
                filters: {},
                search: {
                    value: req.body['search[value]']
                },
                order: [{
                    column: 1, // name column
                    dir: 'DESC'
                }],
                start: isNaN(parseInt(req.body.start)) ? 0 : parseInt(req.body.start),
                length: isNaN(parseInt(req.body.length)) ? 15 : parseInt(req.body.length),
                draw: req.body.draw || 1
            };
            
            commonService.getDataTableData(parameter).then(async responseData => {
                // Lấy thông tin người tạo và tính tổng số động
                if (responseData.data && responseData.data.length > 0) {
                    for (let item of responseData.data) {
                        if (item.created_by) {
                            const userRes = await commonService.getAllDataTable('user', { id: item.created_by });
                            if (userRes.success && userRes.data && userRes.data.length > 0) {
                                item.created_by_name = userRes.data[0].fullname;
                            } else {
                                item.created_by_name = 'N/A';
                            }
                        } else {
                            item.created_by_name = 'N/A';
                        }
                        
                        // Đếm số lượng thực phẩm trong món ăn
                        const countRes = await commonService.getListTable('SELECT COUNT(*) as count FROM dish_foods WHERE dish_id = ?', [item.id]);
                        if (countRes.success && countRes.data && countRes.data.length > 0) {
                            item.food_count = countRes.data[0].count;
                        } else {
                            item.food_count = 0;
                        }
                        
                        // Tính tổng khối lượng và năng lượng động
                        const dishTotalsRes = await commonService.getListTable(`
                            SELECT 
                                SUM(df.weight) as total_weight,
                                SUM((mn.energy * df.weight / 100)) as total_energy
                            FROM dish_foods df
                            LEFT JOIN main_nutrients mn ON df.food_id = mn.id_food
                            WHERE df.dish_id = ?
                        `, [item.id]);
                        
                        if (dishTotalsRes.success && dishTotalsRes.data && dishTotalsRes.data.length > 0) {
                            item.total_weight = parseFloat(dishTotalsRes.data[0].total_weight || 0);
                            item.total_energy = parseFloat(dishTotalsRes.data[0].total_energy || 0);
                        } else {
                            item.total_weight = 0;
                            item.total_energy = 0;
                        }
                    }
                }
                res.json(responseData);
            }).catch(error => {
                commonService.saveLog(req, error.message, error.stack);
                res.json({
                    draw: req.body.draw || 1,
                    recordsTotal: 0,
                    recordsFiltered: 0,
                    data: [],
                    error: 'Có lỗi xảy ra khi tải dữ liệu'
                });
            });
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json({
                draw: req.body.draw || 1,
                recordsTotal: 0,
                recordsFiltered: 0,
                data: [],
                error: 'Có lỗi xảy ra khi tải dữ liệu'
            });
        }
    },
    
    // API tạo/cập nhật món ăn
    upsert: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: null
        };
        try {
            const validateRules = [
                { field: "name", type: "string", required: true, message: "Vui lòng nhập tên món ăn!" }
            ];
            
            const parameter = {
                name: req.body.name,
                description: req.body.description || '',
                category: req.body.category || '',
                created_by: req.user.id
            };
            
            // Validate input
            const errors = securityService.validateInput(parameter, validateRules, { returnType: 'array' });
            if (errors.length > 0) {
                resultData.message = errors.map(s => s.message).join(', ');
                return res.json(resultData);
            }
            
            // Parse danh sách thực phẩm
            let dishFoods = [];
            if (req.body.dish_foods) {
                try {
                    dishFoods = JSON.parse(req.body.dish_foods);
                } catch (e) {
                    resultData.message = 'Dữ liệu thực phẩm không hợp lệ!';
                    return res.json(resultData);
                }
            }
            
            const isCreate = !req.body.id;
            let responseData;
            let dishId;
            
            if (isCreate) {
                // Thêm mới món ăn
                responseData = await commonService.addRecordTable(parameter, 'dishes', true);
                if (responseData.success && responseData.data) {
                    dishId = responseData.data.insertId;
                    resultData.data = { id: dishId };
                }
            } else {
                dishId = req.body.id;
                delete parameter.created_by; // Không cập nhật created_by khi edit
                
                // Cập nhật món ăn
                responseData = await commonService.updateRecordTable(parameter, { id: dishId }, 'dishes');
            }
            
            if (responseData.success) {
                // Xóa tất cả thực phẩm cũ trong món ăn
                await commonService.deleteRecordTable({ dish_id: dishId }, {}, 'dish_foods');
                
                // Thêm lại danh sách thực phẩm mới (không tính tổng số)
                for (let i = 0; i < dishFoods.length; i++) {
                    const food = dishFoods[i];
                    const weight = parseFloat(food.weight) || 0;
                    
                    if (food.food_id && weight > 0) {
                        // Thêm vào bảng dish_foods
                        await commonService.addRecordTable({
                            dish_id: dishId,
                            food_id: food.food_id,
                            weight: weight,
                            order_index: i
                        }, 'dish_foods');
                    }
                }
            }
            
            resultData.success = responseData.success;
            resultData.message = responseData.success 
                ? (isCreate ? 'Lưu thành công!' : 'Cập nhật thành công!')
                : responseData.message;
                
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            resultData.message = 'Đã xảy ra lỗi trong quá trình xử lý!';
        }
        
        res.json(resultData);
    },
    
    // API xóa món ăn
    delete: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: null,
            error: null
        };

        try {
            const { id } = req.params;
            const user = req.user;

            // Kiểm tra quyền truy cập
            if (!user.isAdmin) {
                throw new Error('Bạn không có quyền xóa danh sách này!');
            }

            // Kiểm tra ID
            if (!id) {
                throw new Error('Thiếu ID bản ghi!');
            }

            const recordId = parseInt(id, 10);
            if (isNaN(recordId)) {
                throw new Error('ID bản ghi không hợp lệ!');
            }

            // Xóa bản ghi (soft delete)
            const updateData = { active: -1 };
            const conditions = { id: recordId };
            
            const responseData = await commonService.updateRecordTable(updateData, conditions, 'dishes');

            if (!responseData || !responseData.success) {
                throw new Error('Không thể xóa bản ghi!');
            }

            resultData.success = responseData.success;
            resultData.message = 'Xóa món ăn thành công!';
            resultData.data = responseData.data || null;

            return res.status(200).json(resultData);

        } catch (error) {
            commonService.saveLog(req, error.message, error.stack)
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    
    // API lấy danh sách món ăn cho select
    getDishesForSelect: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: []
        };
        
        try {
            const dishesRes = await commonService.getAllDataTable('dishes', { active: 1 });
            if (dishesRes.success && dishesRes.data) {
                resultData.success = true;
                resultData.data = dishesRes.data.map(dish => ({
                    value: dish.id,
                    label: dish.name,
                    description: dish.description,
                    category: dish.category
                }));
            } else {
                resultData.message = 'Không có dữ liệu món ăn';
            }
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack)
            resultData.message = 'Có lỗi xảy ra khi lấy danh sách món ăn';
        }
        
        res.json(resultData);
    },
    
    // API lấy chi tiết thực phẩm trong món ăn
    getDishFoods: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: []
        };
        
        try {
            const dishId = req.params.id;
            if (!dishId) {
                resultData.message = 'Thiếu ID món ăn';
                return res.json(resultData);
            }
            
            // Kiểm tra món ăn có tồn tại không
            const dishCheckRes = await commonService.getListTable(`SELECT id, name FROM dishes WHERE id = ?`, [dishId]);
            
            if (!dishCheckRes.success || !dishCheckRes.data || dishCheckRes.data.length === 0) {
                resultData.message = 'Món ăn không tồn tại';
                return res.json(resultData);
            }
            
            // Kiểm tra dữ liệu trong dish_foods đơn giản
            const simpleDishFoodsRes = await commonService.getListTable(`
                SELECT * FROM dish_foods WHERE dish_id = ?
            `, [dishId]);
            
            if (!simpleDishFoodsRes.success || !simpleDishFoodsRes.data || simpleDishFoodsRes.data.length === 0) {
                resultData.message = 'Không có dữ liệu thực phẩm trong món ăn';
                return res.json(resultData);
            }
            
            // Query đầy đủ với tất cả các trường từ food_info và main_nutrients
            const dishFoodsRes = await commonService.getListTable(`
                SELECT 
                    df.*,
                    -- Tất cả các trường từ food_info
                    fi.id as food_info_id,
                    fi.code as food_code,
                    fi.name as food_name,
                    fi.ten as food_ten,
                    fi.type as food_type,
                    fi.type_year as food_type_year,
                    fi.total_sugar as food_total_sugar,
                    fi.galactose as food_galactose,
                    fi.maltose as food_maltose,
                    fi.lactose as food_lactose,
                    fi.fructose as food_fructose,
                    fi.glucose as food_glucose,
                    fi.sucrose as food_sucrose,
                    fi.lycopene as food_lycopene,
                    fi.lutein_zeaxanthin as food_lutein_zeaxanthin,
                    fi.total_isoflavone as food_total_isoflavone,
                    fi.daidzein as food_daidzein,
                    fi.genistein as food_genistein,
                    fi.glycetin as food_glycetin,
                    fi.phytosterol as food_phytosterol,
                    fi.purine as food_purine,
                    fi.weight as food_weight,
                    fi.protein as food_protein,
                    fi.in as food_in,
                    fi.lysin as food_lysin,
                    fi.methionin as food_methionin,
                    fi.tryptophan as food_tryptophan,
                    fi.phenylalanin as food_phenylalanin,
                    fi.threonin as food_threonin,
                    fi.isoleucine as food_isoleucine,
                    fi.arginine as food_arginine,
                    fi.histidine as food_histidine,
                    fi.alanine as food_alanine,
                    fi.aspartic_acid as food_aspartic_acid,
                    fi.glutamic_acid as food_glutamic_acid,
                    fi.proline as food_proline,
                    fi.serine as food_serine,
                    fi.animal_protein as food_animal_protein,
                    fi.cystine as food_cystine,
                    fi.valine as food_valine,
                    fi.tyrosine as food_tyrosine,
                    fi.leucine as food_leucine,
                    fi.lignoceric as food_lignoceric_fi,
                    fi.unanimal_lipid as food_unanimal_lipid,
                    fi.retinol as food_retinol,
                    fi.riboflavin as food_riboflavin,
                    fi.thiamine as food_thiamine,
                    fi.niacin as food_niacin,
                    fi.pantothenic_acid as food_pantothenic_acid,
                    fi.folate as food_folate,
                    fi.folic_acid as food_folic_acid,
                    fi.biotin as food_biotin,
                    fi.caroten as food_caroten,
                    fi.vitamin_a_rae as food_vitamin_a_rae,
                    fi.vitamin_b1 as food_vitamin_b1,
                    fi.vitamin_b2 as food_vitamin_b2,
                    fi.vitamin_b6 as food_vitamin_b6,
                    fi.vitamin_b12 as food_vitamin_b12,
                    fi.vitamin_pp as food_vitamin_pp,
                    fi.vitamin_c as food_vitamin_c,
                    fi.vitamin_e as food_vitamin_e,
                    fi.vitamin_k as food_vitamin_k,
                    fi.b_carotene as food_b_carotene,
                    fi.a_carotene as food_a_carotene,
                    fi.b_cryptoxanthin as food_b_cryptoxanthin,
                    fi.created_by as food_created_by,
                    fi.created_at as food_created_at,
                    fi.updated_at as food_updated_at,
                    
                    -- Tất cả các trường từ main_nutrients
                    mn.id as nutrients_id,
                    mn.id_food as nutrients_id_food,
                    mn.edible as food_edible,
                    mn.energy as food_energy,
                    mn.water as food_water,
                    mn.protein as nutrients_protein,
                    mn.fat as food_fat,
                    mn.carbohydrate as food_carbohydrate,
                    mn.fiber as food_fiber,
                    mn.ash as food_ash,
                    mn.calci as food_calci,
                    mn.phosphorous as food_phosphorous,
                    mn.fe as food_fe,
                    mn.zinc as food_zinc,
                    mn.sodium as food_sodium,
                    mn.potassium as food_potassium,
                    mn.magnesium as food_magnesium,
                    mn.manganese as food_manganese,
                    mn.copper as food_copper,
                    mn.selenium as food_selenium,
                    mn.total_fat as food_total_fat,
                    mn.total_saturated_fat as food_total_saturated_fat,
                    mn.palmitic as food_palmitic,
                    mn.margaric as food_margaric,
                    mn.stearic as food_stearic,
                    mn.arachidic as food_arachidic,
                    mn.behenic as food_behenic,
                    mn.lignoceric as food_lignoceric_mn,
                    mn.mufa as food_mufa,
                    mn.myristoleic as food_myristoleic,
                    mn.palmitoleic as food_palmitoleic,
                    mn.oleic as food_oleic,
                    mn.fufa as food_fufa,
                    mn.linoleic as food_linoleic,
                    mn.linolenic as food_linolenic,
                    mn.arachidonic as food_arachidonic,
                    mn.dha as food_dha,
                    mn.trans_fatty_acids as food_trans_fatty_acids,
                    mn.cholesterol as food_cholesterol
                FROM dish_foods df
                LEFT JOIN food_info fi ON df.food_id = fi.id
                LEFT JOIN main_nutrients mn ON fi.id = mn.id_food
                WHERE df.dish_id = ?
                ORDER BY df.order_index ASC, df.id ASC
            `, [dishId]);
            
            if (dishFoodsRes.success && dishFoodsRes.data && dishFoodsRes.data.length > 0) {
                resultData.success = true;
                resultData.message = `Tìm thấy ${dishFoodsRes.data.length} thực phẩm trong món ăn`;
                
                // Tính toán dinh dưỡng theo khối lượng thực tế
                resultData.data = dishFoodsRes.data.map(food => {
                    const ratio = food.weight / 100; // food_info lưu theo 100g
                    
                    const calculatedNutrients = {
                        // Các giá trị chính đã tính toán
                        calculated_energy: (parseFloat(food.food_energy) || 0) * ratio,
                        calculated_protein: (parseFloat(food.food_protein) || 0) * ratio,
                        calculated_fat: (parseFloat(food.food_fat) || 0) * ratio,
                        calculated_carbohydrate: (parseFloat(food.food_carbohydrate) || 0) * ratio,
                        calculated_animal_protein: (parseFloat(food.food_animal_protein) || 0) * ratio,
                        calculated_unanimal_lipid: (parseFloat(food.food_unanimal_lipid) || 0) * ratio,
                        
                        // Tính toán các chất dinh dưỡng cơ bản
                        calculated_water: (parseFloat(food.food_water) || 0) * ratio,
                        calculated_fiber: (parseFloat(food.food_fiber) || 0) * ratio,
                        calculated_ash: (parseFloat(food.food_ash) || 0) * ratio,
                        calculated_edible: (parseFloat(food.food_edible) || 0) * ratio,
                        
                        // Tính toán khoáng chất
                        calculated_calci: (parseFloat(food.food_calci) || 0) * ratio,
                        calculated_phosphorous: (parseFloat(food.food_phosphorous) || 0) * ratio,
                        calculated_fe: (parseFloat(food.food_fe) || 0) * ratio,
                        calculated_zinc: (parseFloat(food.food_zinc) || 0) * ratio,
                        calculated_sodium: (parseFloat(food.food_sodium) || 0) * ratio,
                        calculated_potassium: (parseFloat(food.food_potassium) || 0) * ratio,
                        calculated_magnesium: (parseFloat(food.food_magnesium) || 0) * ratio,
                        calculated_manganese: (parseFloat(food.food_manganese) || 0) * ratio,
                        calculated_copper: (parseFloat(food.food_copper) || 0) * ratio,
                        calculated_selenium: (parseFloat(food.food_selenium) || 0) * ratio,
                        
                        // Tính toán chất béo
                        calculated_total_fat: (parseFloat(food.food_total_fat) || 0) * ratio,
                        calculated_total_saturated_fat: (parseFloat(food.food_total_saturated_fat) || 0) * ratio,
                        calculated_palmitic: (parseFloat(food.food_palmitic) || 0) * ratio,
                        calculated_margaric: (parseFloat(food.food_margaric) || 0) * ratio,
                        calculated_stearic: (parseFloat(food.food_stearic) || 0) * ratio,
                        calculated_arachidic: (parseFloat(food.food_arachidic) || 0) * ratio,
                        calculated_behenic: (parseFloat(food.food_behenic) || 0) * ratio,
                        calculated_lignoceric_mn: (parseFloat(food.food_lignoceric_mn) || 0) * ratio,
                        calculated_mufa: (parseFloat(food.food_mufa) || 0) * ratio,
                        calculated_myristoleic: (parseFloat(food.food_myristoleic) || 0) * ratio,
                        calculated_palmitoleic: (parseFloat(food.food_palmitoleic) || 0) * ratio,
                        calculated_oleic: (parseFloat(food.food_oleic) || 0) * ratio,
                        calculated_fufa: (parseFloat(food.food_fufa) || 0) * ratio,
                        calculated_linoleic: (parseFloat(food.food_linoleic) || 0) * ratio,
                        calculated_linolenic: (parseFloat(food.food_linolenic) || 0) * ratio,
                        calculated_arachidonic: (parseFloat(food.food_arachidonic) || 0) * ratio,
                        calculated_dha: (parseFloat(food.food_dha) || 0) * ratio,
                        calculated_trans_fatty_acids: (parseFloat(food.food_trans_fatty_acids) || 0) * ratio,
                        calculated_cholesterol: (parseFloat(food.food_cholesterol) || 0) * ratio,
                        
                        // Tính toán đường
                        calculated_total_sugar: (parseFloat(food.food_total_sugar) || 0) * ratio,
                        calculated_galactose: (parseFloat(food.food_galactose) || 0) * ratio,
                        calculated_maltose: (parseFloat(food.food_maltose) || 0) * ratio,
                        calculated_lactose: (parseFloat(food.food_lactose) || 0) * ratio,
                        calculated_fructose: (parseFloat(food.food_fructose) || 0) * ratio,
                        calculated_glucose: (parseFloat(food.food_glucose) || 0) * ratio,
                        calculated_sucrose: (parseFloat(food.food_sucrose) || 0) * ratio,
                        
                        // Tính toán amino acid
                        calculated_in: (parseFloat(food.food_in) || 0) * ratio,
                        calculated_lysin: (parseFloat(food.food_lysin) || 0) * ratio,
                        calculated_methionin: (parseFloat(food.food_methionin) || 0) * ratio,
                        calculated_tryptophan: (parseFloat(food.food_tryptophan) || 0) * ratio,
                        calculated_phenylalanin: (parseFloat(food.food_phenylalanin) || 0) * ratio,
                        calculated_threonin: (parseFloat(food.food_threonin) || 0) * ratio,
                        calculated_isoleucine: (parseFloat(food.food_isoleucine) || 0) * ratio,
                        calculated_arginine: (parseFloat(food.food_arginine) || 0) * ratio,
                        calculated_histidine: (parseFloat(food.food_histidine) || 0) * ratio,
                        calculated_alanine: (parseFloat(food.food_alanine) || 0) * ratio,
                        calculated_aspartic_acid: (parseFloat(food.food_aspartic_acid) || 0) * ratio,
                        calculated_glutamic_acid: (parseFloat(food.food_glutamic_acid) || 0) * ratio,
                        calculated_proline: (parseFloat(food.food_proline) || 0) * ratio,
                        calculated_serine: (parseFloat(food.food_serine) || 0) * ratio,
                        calculated_cystine: (parseFloat(food.food_cystine) || 0) * ratio,
                        calculated_valine: (parseFloat(food.food_valine) || 0) * ratio,
                        calculated_tyrosine: (parseFloat(food.food_tyrosine) || 0) * ratio,
                        calculated_leucine: (parseFloat(food.food_leucine) || 0) * ratio,
                        
                        // Tính toán vitamin
                        calculated_retinol: (parseFloat(food.food_retinol) || 0) * ratio,
                        calculated_riboflavin: (parseFloat(food.food_riboflavin) || 0) * ratio,
                        calculated_thiamine: (parseFloat(food.food_thiamine) || 0) * ratio,
                        calculated_niacin: (parseFloat(food.food_niacin) || 0) * ratio,
                        calculated_pantothenic_acid: (parseFloat(food.food_pantothenic_acid) || 0) * ratio,
                        calculated_folate: (parseFloat(food.food_folate) || 0) * ratio,
                        calculated_folic_acid: (parseFloat(food.food_folic_acid) || 0) * ratio,
                        calculated_biotin: (parseFloat(food.food_biotin) || 0) * ratio,
                        calculated_caroten: (parseFloat(food.food_caroten) || 0) * ratio,
                        calculated_vitamin_a_rae: (parseFloat(food.food_vitamin_a_rae) || 0) * ratio,
                        calculated_vitamin_b1: (parseFloat(food.food_vitamin_b1) || 0) * ratio,
                        calculated_vitamin_b2: (parseFloat(food.food_vitamin_b2) || 0) * ratio,
                        calculated_vitamin_b6: (parseFloat(food.food_vitamin_b6) || 0) * ratio,
                        calculated_vitamin_b12: (parseFloat(food.food_vitamin_b12) || 0) * ratio,
                        calculated_vitamin_pp: (parseFloat(food.food_vitamin_pp) || 0) * ratio,
                        calculated_vitamin_c: (parseFloat(food.food_vitamin_c) || 0) * ratio,
                        calculated_vitamin_e: (parseFloat(food.food_vitamin_e) || 0) * ratio,
                        calculated_vitamin_k: (parseFloat(food.food_vitamin_k) || 0) * ratio,
                        calculated_b_carotene: (parseFloat(food.food_b_carotene) || 0) * ratio,
                        calculated_a_carotene: (parseFloat(food.food_a_carotene) || 0) * ratio,
                        calculated_b_cryptoxanthin: (parseFloat(food.food_b_cryptoxanthin) || 0) * ratio,
                        
                        // Tính toán các chất khác
                        calculated_lycopene: (parseFloat(food.food_lycopene) || 0) * ratio,
                        calculated_lutein_zeaxanthin: (parseFloat(food.food_lutein_zeaxanthin) || 0) * ratio,
                        calculated_total_isoflavone: (parseFloat(food.food_total_isoflavone) || 0) * ratio,
                        calculated_daidzein: (parseFloat(food.food_daidzein) || 0) * ratio,
                        calculated_genistein: (parseFloat(food.food_genistein) || 0) * ratio,
                        calculated_glycetin: (parseFloat(food.food_glycetin) || 0) * ratio,
                        calculated_phytosterol: (parseFloat(food.food_phytosterol) || 0) * ratio,
                        calculated_purine: (parseFloat(food.food_purine) || 0) * ratio,
                        calculated_lignoceric_fi: (parseFloat(food.food_lignoceric_fi) || 0) * ratio
                    };
                    
                    return {
                        ...food,
                        ...calculatedNutrients
                    };
                });
            } else {
                resultData.message = 'Không có dữ liệu thực phẩm trong món ăn';
            }
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack)
            resultData.message = 'Có lỗi xảy ra khi lấy danh sách thực phẩm';
        }
        
        res.json(resultData);
    }
};

module.exports = dishController; 