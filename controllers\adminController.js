var bcrypt = require('bcrypt'),
    commonService   = require('../services/commonService'),
    securityService = require('../services/securityService');

let adminService = {
    index: (req, res) => {
        try {
            if(!req.user.isAdmin){
                errors.push('Bạn không có quyền truy cập danh sách này!');
            }
            res.render('admin/index', {user: req.user})
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            return res.render("error");
        }
    },
    user: async (req, res) =>{
        try {
            let errors = [];
            let campaigns = [];
            if(!req.user.isAdmin){
                errors.push('Bạn không có quyền truy cập danh sách này!');
            }
            const listCampaign = await commonService.getAllDataTable('campaign', {active: 1});
            if (listCampaign.success && listCampaign.data) {
                campaigns = listCampaign.data.map(item => ({
                    label: item.name,
                    value: item.id
                }));
            }
            res.render('admin/user/list', {
                user: req.user,
                errors: errors,
                campaigns: JSON.stringify(campaigns)
            })
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            return res.render("error");
        }
    },
    menuExample: (req, res) =>{
        try {
            let errors = [];
            if(!req.user.isAdmin){
                errors.push('Bạn không có quyền truy cập danh sách này!');
            }
            res.render('admin/menuExample', {
                user: req.user,
                errors: errors
            })
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            return res.render("error");
        }
    },
    getDataEditTable: (req, res) =>{
        try {
            var resultData = {
                success: false,
                message: "",
                data: {}
            };
    
            let table = req.body.table;
            if(!req.user.isAdmin){
                resultData.message = 'Bạn không có quyền truy cập danh sách này!';
                return res.json(resultData);
            }
            if(!table){
                resultData.message = 'Thiếu dữ liệu bảng!';
                return res.json(resultData);
            }
            commonService.getAllDataTable(table, { id: req.body.id}).then(async responseData =>{
                if(responseData.success){
                    resultData.success = true;
                    if(responseData.data && responseData.data.length > 0){
                        resultData.data = responseData.data[0];
                        if(table == 'user'){
                            let role_user = await commonService.getAllDataTable('role_user', {user_id: req.body.id});
                            if(role_user.success && role_user.data && role_user.data.length > 0){
                                resultData.data['role'] = role_user.data.map(item => item.role_id);
                            }
                        } else if(table == 'menu_example'){
                            // Không cần xử lý thêm gì cho menu_example
                        } else if(table == 'food_info'){
                            // Lấy thông tin main_nutrients
                            let main_nutrients = await commonService.getAllDataTable('main_nutrients', {id_food: req.body.id});
                            if(main_nutrients.success && main_nutrients.data && main_nutrients.data.length > 0){
                                resultData.data['main_nutrients'] = main_nutrients.data[0];
                            }
                        }
                        
                    }else{
                        resultData.message = 'Không có dữ liệu';
                    }
                }else{
                    resultData.message = responseData.message;
                }
                return res.json(resultData);
            }).catch(error => {
                commonService.saveLog(req, error.message, error.stack);
                res.json({
                    success: false,
                    message: "Có lỗi xảy ra, vui lòng thử lại sau!",
                    data: {}
                });
            });
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse("Có lỗi xảy ra, vui lòng thử lại sau!"));
        }
    },
    userList: (req, res) =>{
        try {
            var parameter = {
                    table: 'user',
                    columns: ['id', 'fullname', 'email', 'active', 'campaign_id'],
                    primaryKey: 'id',
                    active: -1,
                    activeOperator: '!=',
                    filters: {
                        
                    },
                    search: {
                        value: req.body['search[value]']
                    },
                    order: [{
                        column: 1, // name column
                        dir: 'DESC'
                    }],
                    start: isNaN(parseInt(req.body.start)) ? 0 : parseInt(req.body.start),
                    length: isNaN(parseInt(req.body.length)) ? 15 : parseInt(req.body.length),
                    draw: req.body.draw || 1
                };
            commonService.getDataTableData(parameter).then(async responseData =>{
                let role_user = await commonService.getAllDataTable('role_user',{});
                if(role_user.success && role_user.data && role_user.data.length > 0){
                    for(let item of responseData.data){
                        let userRoles = role_user.data.filter(r => item.id == r.user_id).map(r => r.role_id);
                        item['role'] = userRoles;
                    }
                }
                // Đảm bảo tất cả user đều có thuộc tính role
                for(let item of responseData.data){
                    if(!item.hasOwnProperty('role')){
                        item['role'] = [];
                    }
                }
                res.json(responseData)
            }).catch(error => {
                commonService.saveLog(req, error.message, error.stack);
                res.json({
                    "data": [],
                    "error": "Có lỗi xảy ra, vui lòng thử lại sau!",
                    "draw": "1",
                    "recordsFiltered": 0,
                    "recordsTotal": 0
                });
            });
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json({
                "data": [],
                "error": "Có lỗi xảy ra, vui lòng thử lại sau!",
                "draw": "1",
                "recordsFiltered": 0,
                "recordsTotal": 0
            });
        }
    },
    userUpsert: async (req, res) => {
        try {
            var resultData = {
                success: false,
                message: "",
                data: ''
            };
        
            // Quy tắc validate chung
            const validateRules = [
                { field: "fullname", type: "string", required: true, message: "Vui lòng nhập họ tên!" },
                { field: "email", type: "string", required: true, message: "Vui lòng nhập email!", 
                    customValidator: (value) => {
                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        return emailRegex.test(value) ? null : "Định dạng email không hợp lệ";
                    } 
                }
            ];
        
            // Nếu là tạo mới, thêm validate cho password
            const isCreate = !req.body.id;
            if (isCreate) {
                validateRules.push({ 
                    field: "password", 
                    type: "string", 
                    required: true, 
                    message: "Vui lòng nhập mật khẩu!" 
                });
            }
        
            // Chuẩn bị tham số
            const parameter = {
                fullname: req.body.fullname,
                email: req.body.email,
                phone: req.body.phone,
                password: req.body.password,
                gender: req.body.gender ? parseInt(req.body.gender) : 0,
                campaign_id: req.body.campaign_id ? parseInt(req.body.campaign_id) : 0,
                active: req.body.active ? parseInt(req.body.active) : 0,
            };
            
            // Xử lý role từ multi-select
            let role_ids = [];
            if (req.body.role) {
                // Nếu role là array (từ AJAX)
                if (Array.isArray(req.body.role)) {
                    role_ids = req.body.role;
                } else {
                    role_ids = [parseInt(req.body.role)];
                }
            } else if (req.body['role[]']) {
                // Nếu role là role[] (từ form serialize)
                if (Array.isArray(req.body['role[]'])) {
                    role_ids = req.body['role[]'].map(r => parseInt(r));
                } else {
                    role_ids = [parseInt(req.body['role[]'])];
                }
            }
        
            // Kiểm tra quyền admin
            if (!req.user.isAdmin) {
                resultData.message = 'Bạn không có quyền thực hiện thao tác này!';
                return res.json(resultData);
            }
        
            // Validate input
            const errors = securityService.validateInput(parameter, validateRules, { returnType: 'array' });
            if (errors.length > 0) {
                resultData.message = errors.map(s => s.message).join(', ');
                return res.json(resultData);
            }
        
            // Validate role_ids
            if (!role_ids || role_ids.length === 0) {
                resultData.message = 'Vui lòng chọn quyền!';
                return res.json(resultData);
            }
        
            // Xử lý password
            if (parameter.password) {
                parameter.password = await bcrypt.hash(parameter.password, 10);
            } else if (!isCreate) {
                delete parameter.password; // Không cập nhật password nếu không có giá trị
            }
        
            // Lưu role_ids và xóa role khỏi parameter
            delete parameter.role;
    
            let responseData;
            if (isCreate) {
                // Kiểm tra email tồn tại
                let checkEmail = await commonService.getAllDataTable('user', {email: parameter.email});
                if(checkEmail.success && checkEmail.data && checkEmail.data.length > 0){
                    resultData.message = 'Email ' + parameter.email + ' đã tồn tại! Vui lòng chọn email khác.';
                    return res.json(resultData);
                }
                // Thêm mới user
                responseData = await commonService.addRecordTable(parameter, 'user', true);
                if (responseData.success && responseData.data) {
                    const userId = responseData.data.insertId;
                    // Thêm tất cả role cho user
                    for (let role_id of role_ids) {
                        await commonService.addRecordTable({ role_id, user_id: userId }, 'role_user');
                    }
                    resultData.data = { id: userId };
                }
            } else {
                // Kiểm tra email tồn tại
                let checkEmail = await commonService.getListTable('SELECT * FROM user WHERE email = ? AND id != ?', [parameter.email, req.body.id]);
                if(checkEmail.success && checkEmail.data && checkEmail.data.length > 0){
                    resultData.message = 'Email ' + parameter.email + ' đã tồn tại! Vui lòng chọn email khác.';
                    return res.json(resultData);
                }
                // Cập nhật user
                responseData = await commonService.updateRecordTable(parameter, { id: req.body.id }, 'user');
                if (responseData.success && responseData.data) {
                    // Lấy danh sách role hiện tại của user
                    let currentRolesRes = await commonService.getAllDataTable('role_user', { user_id: req.body.id });
                    let currentRoles = [];
                    if(currentRolesRes.success && currentRolesRes.data) {
                        currentRoles = currentRolesRes.data.map(r => r.role_id);
                    }
                    // Tìm role cần xóa (có trong DB nhưng không có trong role_ids)
                    let rolesToDelete = currentRoles.filter(r => !role_ids.includes(r));
                    // Tìm role cần thêm (có trong role_ids nhưng không có trong DB)
                    let rolesToAdd = role_ids.filter(r => !currentRoles.includes(r));
                    // Xóa role không còn
                    for(let role_id of rolesToDelete) {
                        await commonService.deleteRecordTable({ user_id: req.body.id, role_id }, {}, 'role_user');
                    }
                    // Thêm role mới
                    for(let role_id of rolesToAdd) {
                        await commonService.addRecordTable({ role_id, user_id: req.body.id }, 'role_user');
                    }
                }
            }
    
            resultData.success = responseData.success;
            resultData.message = responseData.success 
                ? (isCreate ? 'Lưu thành công!' : 'Cập nhật thành công!')
                : responseData.message;
            res.json(resultData);
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse("Có lỗi xảy ra, vui lòng thử lại sau!"));
        }
    },
    userDelete: async (req, res) =>{
        try {
            // Khởi tạo response mặc định
            const resultData = {
                success: false,
                message: '',
                data: null,
                error: null
            };
    
            // Lấy tham số từ request
            const { id } = req.params;
            const user = req.user;
            const table = 'user';
    
            // Kiểm tra quyền truy cập
            const allowedRoles = [1]; // Có thể mở rộng danh sách vai trò
            const hasPermission = user.isAdmin || (user.role_id && allowedRoles.some(role => user.role_id.includes(role)));
            
            if (!hasPermission) {
                throw new Error('Bạn không có quyền xóa danh sách này!');
            }
    
            // Kiểm tra ID
            if (!id) {
                throw new Error('Thiếu ID bản ghi!');
            }
    
            // Validate ID (giả sử ID là số)
            const recordId = parseInt(id, 10);
            if (isNaN(recordId)) {
                throw new Error('ID bản ghi không hợp lệ!');
            }
    
            // Cập nhật bản ghi
            const updateData = { active: -1 };
            const conditions = { id: recordId };
            
            const responseData = await commonService.updateRecordTable(updateData, conditions, table);
    
            // Xử lý kết quả
            if (!responseData || !responseData.success) {
                throw new Error('Không thể cập nhật bản ghi!');
            }
    
            resultData.success = responseData.success;
            resultData.message = responseData.success ? 'Xóa bản ghi thành công!' : responseData.message;
            resultData.data = responseData.data || null;
    
            return res.status(200).json(resultData);
    
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse("Có lỗi xảy ra, vui lòng thử lại sau!"));
        }
    },
    
    // Campaign methods
    campaign: async (req, res) => {
        try {
            let errors = [];
            if(!req.user.isAdmin){
                errors.push('Bạn không có quyền truy cập danh sách này!');
            }
            res.render('admin/campaign/list', {
                user: req.user,
                errors: errors
            })
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            return res.render("error");
        }
    },
    
    campaignList: (req, res) => {
        try {
            var parameter = {
                table: 'campaign',
                columns: ['id', 'name', 'active', 'created_by', 'created_at'],
                primaryKey: 'id',
                active: -1,
                activeOperator: '!=',
                filters: {
                    
                },
                search: {
                    value: req.body['search[value]']
                },
                order: [{
                    column: 1, // name column
                    dir: 'DESC'
                }],
                start: isNaN(parseInt(req.body.start)) ? 0 : parseInt(req.body.start),
                length: isNaN(parseInt(req.body.length)) ? 15 : parseInt(req.body.length),
                draw: req.body.draw || 1
            };
            
            commonService.getDataTableData(parameter).then(async responseData => {
                // Lấy thông tin người tạo
                if (responseData.data && responseData.data.length > 0) {
                    for (let item of responseData.data) {
                        if (item.created_by) {
                            const userRes = await commonService.getAllDataTable('user', { id: item.created_by });
                            if (userRes.success && userRes.data && userRes.data.length > 0) {
                                item.created_by_name = userRes.data[0].fullname;
                            } else {
                                item.created_by_name = 'N/A';
                            }
                        } else {
                            item.created_by_name = 'N/A';
                        }
                    }
                }
                res.json(responseData);
            }).catch(error => {
                commonService.saveLog(req, error.message, error.stack);
                res.json({
                    "data": [],
                    "error": "Có lỗi xảy ra, vui lòng thử lại sau!",
                    "draw": "1",
                    "recordsFiltered": 0,
                    "recordsTotal": 0
                });
            });
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json({
                "data": [],
                "error": "Có lỗi xảy ra, vui lòng thử lại sau!",
                "draw": "1",
                "recordsFiltered": 0,
                "recordsTotal": 0
            });
        }
    },
    
    campaignUpsert: async (req, res) => {
        try {
            var resultData = {
                success: false,
                message: "",
                data: ''
            };
        
            // Quy tắc validate
            const validateRules = [
                { field: "name", type: "string", required: true, message: "Vui lòng nhập tên chiến dịch!" }
            ];
        
            // Chuẩn bị tham số
            const parameter = {
                name: req.body.name,
                active: req.body.active ? parseInt(req.body.active) : 1,
                created_by: req.user.id
            };
            
            const isCreate = !req.body.id;
        
            // Kiểm tra quyền admin
            if (!req.user.isAdmin) {
                resultData.message = 'Bạn không có quyền thực hiện thao tác này!';
                return res.json(resultData);
            }
        
            // Validate input
            const errors = securityService.validateInput(parameter, validateRules, { returnType: 'array' });
            if (errors.length > 0) {
                resultData.message = errors.map(s => s.message).join(', ');
                return res.json(resultData);
            }
        
            let responseData;
            if (isCreate) {
                // Kiểm tra tên campaign tồn tại
                let checkName = await commonService.getAllDataTable('campaign', {name: parameter.name, active: 1});
                if(checkName.success && checkName.data && checkName.data.length > 0){
                    resultData.message = 'Tên chiến dịch "' + parameter.name + '" đã tồn tại! Vui lòng chọn tên khác.';
                    return res.json(resultData);
                }
                // Thêm mới campaign
                responseData = await commonService.addRecordTable(parameter, 'campaign', true);
                if (responseData.success && responseData.data) {
                    resultData.data = { id: responseData.data.insertId };
                }
            } else {
                // Kiểm tra tên campaign tồn tại
                let checkName = await commonService.getListTable('SELECT * FROM campaign WHERE name = ? AND id != ? AND active = 1', [parameter.name, req.body.id]);
                if(checkName.success && checkName.data && checkName.data.length > 0){
                    resultData.message = 'Tên chiến dịch "' + parameter.name + '" đã tồn tại! Vui lòng chọn tên khác.';
                    return res.json(resultData);
                }
                // Cập nhật campaign
                delete parameter.created_by; // Không cập nhật created_by khi edit
                responseData = await commonService.updateRecordTable(parameter, { id: req.body.id }, 'campaign');
            }
    
            resultData.success = responseData.success;
            resultData.message = responseData.success 
                ? (isCreate ? 'Lưu thành công!' : 'Cập nhật thành công!')
                : responseData.message;
            res.json(resultData);
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse("Có lỗi xảy ra, vui lòng thử lại sau!"));
        }
    },
    
    campaignDelete: async (req, res) => {
        try {
            // Khởi tạo response mặc định
            const resultData = {
                success: false,
                message: '',
                data: null,
                error: null
            };
    
            // Lấy tham số từ request
            const { id } = req.params;
            const user = req.user;
            const table = 'campaign';
    
            // Kiểm tra quyền truy cập
            if (!user.isAdmin) {
                throw new Error('Bạn không có quyền xóa danh sách này!');
            }
    
            // Kiểm tra ID
            if (!id) {
                throw new Error('Thiếu ID bản ghi!');
            }
    
            // Validate ID
            const recordId = parseInt(id, 10);
            if (isNaN(recordId)) {
                throw new Error('ID bản ghi không hợp lệ!');
            }
    
            // Kiểm tra xem có user nào đang sử dụng campaign này không
            const usersUsingCampaign = await commonService.getAllDataTable('user', {campaign_id: recordId, active: 1});
            if(usersUsingCampaign.success && usersUsingCampaign.data && usersUsingCampaign.data.length > 0){
                throw new Error('Không thể xóa chiến dịch này vì đang có ' + usersUsingCampaign.data.length + ' người dùng sử dụng!');
            }
    
            // Cập nhật bản ghi (soft delete)
            const updateData = { active: -1 };
            const conditions = { id: recordId };
            
            const responseData = await commonService.updateRecordTable(updateData, conditions, table);
    
            // Xử lý kết quả
            if (!responseData || !responseData.success) {
                throw new Error('Không thể cập nhật bản ghi!');
            }
    
            resultData.success = responseData.success;
            resultData.message = responseData.success ? 'Xóa chiến dịch thành công!' : responseData.message;
            resultData.data = responseData.data || null;
    
            return res.status(200).json(resultData);
    
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || "Có lỗi xảy ra, vui lòng thử lại sau!"));
        }
    },
    
    // Thực đơn mẫu methods
    menuExampleList: async (req, res) => {
        try {
            let errors = [];
            let menuTime = [];
            
            // Lấy danh sách thời gian ăn
            const menuTimeRes = await commonService.getAllDataTable('menu_time', {});
            if (menuTimeRes.success && menuTimeRes.data) {
                menuTime = menuTimeRes.data.map(item => ({
                    id: item.id,
                    name: item.time
                }));
            }
            
            res.render('admin/thuc-don-mau/list', {
                user: req.user,
                errors: errors,
                menuTime: menuTime
            });
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            return res.render("error");
        }
    },
    
    menuExampleDetail: async (req, res) => {
        try {
            const errors = [];
            const user = req.user;
            const id = req.params.id;
            let menuExamine = [];
            let menuTime = [];
            
            // Lấy danh sách thời gian ăn trước
            const menuTimeRes = await commonService.getAllDataTable('menu_time', {});
            if (menuTimeRes.success && menuTimeRes.data) {
                menuTime = menuTimeRes.data.map(item => ({
                    id: item.id,
                    name: item.time
                }));
            }
            
            // Lấy thông tin thực đơn mẫu hiện tại hoặc tạo mới
            if (id && id !== 'new') {
                const currentMenuRes = await commonService.getAllDataTable('menu_example', { id: id });
                if (currentMenuRes.success && currentMenuRes.data && currentMenuRes.data.length > 0) {
                    const currentMenu = currentMenuRes.data[0];
                    // Parse detail JSON
                    if (currentMenu.detail) {
                        try {
                            const detail = JSON.parse(currentMenu.detail);
                            menuExamine = [{
                                id: currentMenu.id,
                                name: currentMenu.name_menu,
                                detail: detail,
                                note: '',
                                isExisting: true
                            }];
                        } catch (e) {
                            // Tạo thực đơn trống nếu không parse được
                            menuExamine = [{
                                id: currentMenu.id,
                                name: currentMenu.name_menu,
                                detail: menuTime.map(time => ({
                                    id: time.id,
                                    name: time.name,
                                    name_course: '',
                                    listFood: []
                                })),
                                note: '',
                                isExisting: true
                            }];
                        }
                    } else {
                        // Tạo thực đơn trống nếu chưa có detail
                        menuExamine = [{
                            id: currentMenu.id,
                            name: currentMenu.name_menu,
                            detail: menuTime.map(time => ({
                                id: time.id,
                                name: time.name,
                                name_course: '',
                                listFood: []
                            })),
                            note: '',
                            isExisting: true
                        }];
                    }
                }
            } else if (id === 'new') {
                // Tạo thực đơn mẫu trống cho chế độ tạo mới
                menuExamine = [{
                    id: 'new',
                    name: 'Thực đơn mới',
                    detail: menuTime.map(time => ({
                        id: time.id,
                        name: time.name,
                        name_course: '',
                        listFood: []
                    })),
                    note: '',
                    isExisting: false
                }];
            }
            
            res.render('admin/thuc-don-mau/index', {
                user: user,
                errors: errors,
                menuExamine: menuExamine,
                menuTime: menuTime
            });
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            return res.render("error");
        }
    },
    
    menuExampleListData: (req, res) => {
        try {
            // Kiểm tra quyền truy cập
            if (!req.user.isAdmin) {
                return res.json({
                    draw: req.body.draw || 1,
                    recordsTotal: 0,
                    recordsFiltered: 0,
                    data: [],
                    error: 'Bạn không có quyền truy cập danh sách này!'
                });
            }
            
            var parameter = {
                table: 'menu_example',
                columns: ['id', 'name_menu', 'created_at', 'share', 'created_by'],
                primaryKey: 'id',
                active: -1,
                activeOperator: '!=',
                activeField: 'share',
                filters: {
                    
                },
                search: {
                    value: req.body['search[value]']
                },
                order: [{
                    column: 1, // name column
                    dir: 'DESC'
                }],
                start: isNaN(parseInt(req.body.start)) ? 0 : parseInt(req.body.start),
                length: isNaN(parseInt(req.body.length)) ? 15 : parseInt(req.body.length),
                draw: req.body.draw || 1
            };
            
            commonService.getDataTableData(parameter).then(async responseData => {
                // Lấy thông tin người tạo
                if (responseData.data && responseData.data.length > 0) {
                    for (let item of responseData.data) {
                        if (item.created_by) {
                            const userRes = await commonService.getAllDataTable('user', { id: item.created_by });
                            if (userRes.success && userRes.data && userRes.data.length > 0) {
                                item.created_by_name = userRes.data[0].fullname;
                            } else {
                                item.created_by_name = 'N/A';
                            }
                        } else {
                            item.created_by_name = 'N/A';
                        }
                    }
                }
                res.json(responseData);
            }).catch(error => {
                commonService.saveLog(req, error.message, error.stack);
                res.json({
                    draw: req.body.draw || 1,
                    recordsTotal: 0,
                    recordsFiltered: 0,
                    data: [],
                    error: 'Có lỗi xảy ra khi tải dữ liệu'
                });
            });
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json({
                draw: req.body.draw || 1,
                recordsTotal: 0,
                recordsFiltered: 0,
                data: [],
                error: 'Có lỗi xảy ra khi tải dữ liệu'
            });
        }
    },
    
    menuExampleUpsert: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: null
        };
        
        try {
            const validateRules = [
                { field: "name_menu", type: "string", required: true, message: "Vui lòng nhập tên thực đơn!" }
            ];
            
            const parameter = {
                name_menu: req.body.name_menu,
                detail: req.body.detail || '[]',
                share: req.body.share ? parseInt(req.body.share) : 0,
                created_by: req.user.id
            };
            
            // Validate input
            const errors = securityService.validateInput(parameter, validateRules, { returnType: 'array' });
            if (errors.length > 0) {
                resultData.message = errors.map(s => s.message).join(', ');
                return res.json(resultData);
            }
            
            const isCreate = !req.body.id;
            let responseData;
            
            if (isCreate) {
                responseData = await commonService.addRecordTable(parameter, 'menu_example', true);
                if (responseData.success && responseData.data) {
                    resultData.data = { id: responseData.data.insertId };
                }
            } else {
                delete parameter.created_by; // Không cập nhật created_by khi edit
                responseData = await commonService.updateRecordTable(parameter, { id: req.body.id }, 'menu_example');
            }
            
            resultData.success = responseData.success;
            resultData.message = responseData.success 
                ? (isCreate ? 'Lưu thành công!' : 'Cập nhật thành công!')
                : responseData.message;
                
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            resultData.message = 'Đã xảy ra lỗi trong quá trình xử lý!';
        }
        
        res.json(resultData);
    },
    
    menuExampleDelete: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: null,
            error: null
        };

        try {
            const { id } = req.params;
            const user = req.user;

            // Kiểm tra quyền truy cập
            if (!user.isAdmin) {
                throw new Error('Bạn không có quyền xóa danh sách này!');
            }

            // Kiểm tra ID
            if (!id) {
                throw new Error('Thiếu ID bản ghi!');
            }

            const recordId = parseInt(id, 10);
            if (isNaN(recordId)) {
                throw new Error('ID bản ghi không hợp lệ!');
            }

            // Xóa bản ghi (soft delete)
            const updateData = { active: -1 };
            const conditions = { id: recordId };
            
            const responseData = await commonService.updateRecordTable(updateData, conditions, 'menu_example');

            if (!responseData || !responseData.success) {
                throw new Error('Không thể xóa bản ghi!');
            }

            resultData.success = responseData.success;
            resultData.message = 'Xóa thực đơn mẫu thành công!';
            resultData.data = responseData.data || null;

            return res.status(200).json(resultData);

        } catch (error) {
            commonService.saveLog(req, error.message, error.stack);
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    },
    
    // Thực phẩm methods
    foodList: (req, res) => {
        try{
            let errors = [];
            res.render('admin/thuc-pham/list', {
                user: req.user,
                errors: errors
            });
        }catch(error){
            commonService.saveLog(req, error.message, error.stack);
            return res.render("error");
        }
    },
    
    foodDetail: async (req, res) => {
        const errors = [];
        const user = req.user;
        const id = req.params.id;
        let foodData = null;
        let mainNutrients = null;
        
        try {
            if (id && id !== 'new') {
                // Lấy thông tin thực phẩm hiện tại
                const currentFoodRes = await commonService.getAllDataTable('food_info', { id: id });
                if (currentFoodRes.success && currentFoodRes.data && currentFoodRes.data.length > 0) {
                    foodData = currentFoodRes.data[0];
                    
                    // Lấy thông tin main_nutrients
                    const nutrientsRes = await commonService.getAllDataTable('main_nutrients', { id_food: id });
                    if (nutrientsRes.success && nutrientsRes.data && nutrientsRes.data.length > 0) {
                        mainNutrients = nutrientsRes.data[0];
                    }
                }
            }
            
        } catch (error) {
            errors.push('Có lỗi xảy ra khi tải dữ liệu: ' + error.message);
            commonService.saveLog(req, error.message, error.stack)
        }
        
        res.render('admin/thuc-pham/index', {
            user: user,
            errors: errors,
            foodData: foodData,
            mainNutrients: mainNutrients,
            isEdit: id && id !== 'new'
        });
    },
    
    foodListData: (req, res) => {
        // Kiểm tra quyền truy cập
        if (!req.user.isAdmin) {
            return res.json({
                draw: req.body.draw || 1,
                recordsTotal: 0,
                recordsFiltered: 0,
                data: [],
                error: 'Bạn không có quyền truy cập danh sách này!'
            });
        }
        
        var parameter = {
            table: 'food_info',
            columns: ['id', 'code', 'name', 'type', 'type_year', 'ten', 'weight', 'protein', 'created_at'],
            primaryKey: 'id',
            filters: {
                
            },
            search: {
                value: req.body['search[value]']
            },
            order: [{
                column: 1, // name column
                dir: 'DESC'
            }],
            start: isNaN(parseInt(req.body.start)) ? 0 : parseInt(req.body.start),
            length: isNaN(parseInt(req.body.length)) ? 15 : parseInt(req.body.length),
            draw: req.body.draw || 1
        };
        
        commonService.getDataTableData(parameter).then(async responseData => {
            // Lấy thông tin energy từ main_nutrients
            if (responseData.data && responseData.data.length > 0) {
                for (let item of responseData.data) {
                    const nutrientsRes = await commonService.getAllDataTable('main_nutrients', { id_food: item.id });
                    if (nutrientsRes.success && nutrientsRes.data && nutrientsRes.data.length > 0) {
                        item.energy = nutrientsRes.data[0].energy || 0;
                    } else {
                        item.energy = 0;
                    }
                }
            }
            res.json(responseData);
        }).catch(error => {
            commonService.saveLog(req, error.message, error.stack)
            res.json({
                draw: req.body.draw || 1,
                recordsTotal: 0,
                recordsFiltered: 0,
                data: [],
                error: 'Có lỗi xảy ra khi tải dữ liệu'
            });
        });
    },
    
    foodUpsert: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: null
        };
        
        try {
            const validateRules = [
                { field: "name", type: "string", required: true, message: "Vui lòng nhập tên thực phẩm!" },
                { field: "weight", type: "number", required: true, message: "Vui lòng nhập khối lượng!" }
            ];
            
            // Chuẩn bị dữ liệu food_info
            const foodParameter = {
                code: req.body.code || '',
                name: req.body.name,
                type: req.body.type || 'raw',
                type_year: req.body.type_year || '2017',
                ten: req.body.ten || '',
                total_sugar: req.body.total_sugar || '',
                galactose: req.body.galactose || '',
                maltose: req.body.maltose || '',
                lactose: req.body.lactose || '',
                fructose: req.body.fructose || '',
                glucose: req.body.glucose || '',
                sucrose: req.body.sucrose || '',
                lycopene: req.body.lycopene || '',
                lutein_zeaxanthin: req.body.lutein_zeaxanthin || '',
                total_isoflavone: req.body.total_isoflavone || '',
                daidzein: req.body.daidzein || '',
                genistein: req.body.genistein || '',
                glycetin: req.body.glycetin || '',
                phytosterol: req.body.phytosterol || '',
                purine: req.body.purine || '',
                weight: parseInt(req.body.weight) || 0,
                protein: req.body.protein || '',
                in: req.body.in || '',
                lysin: req.body.lysin || '',
                methionin: req.body.methionin || '',
                tryptophan: req.body.tryptophan || '',
                phenylalanin: req.body.phenylalanin || '',
                threonin: req.body.threonin || '',
                isoleucine: req.body.isoleucine || '',
                arginine: req.body.arginine || '',
                histidine: req.body.histidine || '',
                alanine: req.body.alanine || '',
                aspartic_acid: req.body.aspartic_acid || '',
                glutamic_acid: req.body.glutamic_acid || '',
                proline: req.body.proline || '',
                serine: req.body.serine || '',
                animal_protein: req.body.animal_protein || '',
                cystine: req.body.cystine || '',
                valine: req.body.valine || '',
                tyrosine: req.body.tyrosine || '',
                leucine: req.body.leucine || '',
                lignoceric: req.body.lignoceric || '',
                unanimal_lipid: req.body.unanimal_lipid || '',
                retinol: req.body.retinol || '',
                riboflavin: req.body.riboflavin || '',
                thiamine: req.body.thiamine || '',
                niacin: req.body.niacin || '',
                pantothenic_acid: req.body.pantothenic_acid || '',
                folate: parseInt(req.body.folate) || null,
                folic_acid: req.body.folic_acid || '',
                biotin: req.body.biotin || '',
                caroten: req.body.caroten || '',
                vitamin_a_rae: req.body.vitamin_a_rae || '',
                vitamin_b1: req.body.vitamin_b1 || '',
                vitamin_b2: req.body.vitamin_b2 || '',
                vitamin_b6: req.body.vitamin_b6 || '',
                vitamin_b12: req.body.vitamin_b12 || '',
                vitamin_pp: req.body.vitamin_pp || '',
                vitamin_c: req.body.vitamin_c || '',
                vitamin_e: req.body.vitamin_e || '',
                vitamin_k: req.body.vitamin_k || '',
                b_carotene: req.body.b_carotene || '',
                a_carotene: req.body.a_carotene || '',
                b_cryptoxanthin: req.body.b_cryptoxanthin || '',
                created_by: req.user.id
            };
            
            // Chuẩn bị dữ liệu main_nutrients
            const nutrientsParameter = {
                edible: parseInt(req.body.edible) || null,
                energy: parseInt(req.body.energy) || null,
                water: req.body.water || '',
                protein: req.body.nutrients_protein || '',
                fat: req.body.fat || '',
                carbohydrate: req.body.carbohydrate || '',
                fiber: req.body.fiber || '',
                ash: req.body.ash || '',
                calci: parseInt(req.body.calci) || null,
                phosphorous: req.body.phosphorous || '',
                fe: req.body.fe || '',
                zinc: req.body.zinc || '',
                sodium: parseInt(req.body.sodium) || null,
                potassium: parseInt(req.body.potassium) || null,
                magnesium: parseInt(req.body.magnesium) || null,
                manganese: req.body.manganese || '',
                copper: parseInt(req.body.copper) || null,
                selenium: req.body.selenium || '',
                total_fat: req.body.total_fat || '',
                total_saturated_fat: req.body.total_saturated_fat || '',
                palmitic: req.body.palmitic || '',
                margaric: req.body.margaric || '',
                stearic: req.body.stearic || '',
                arachidic: req.body.arachidic || '',
                behenic: req.body.behenic || '',
                lignoceric: req.body.nutrients_lignoceric || '',
                mufa: req.body.mufa || '',
                myristoleic: req.body.myristoleic || '',
                palmitoleic: req.body.palmitoleic || '',
                oleic: req.body.oleic || '',
                fufa: req.body.fufa || '',
                linoleic: req.body.linoleic || '',
                linolenic: req.body.linolenic || '',
                arachidonic: req.body.arachidonic || '',
                dha: req.body.dha || '',
                trans_fatty_acids: req.body.trans_fatty_acids || '',
                cholesterol: req.body.cholesterol || ''
            };
            
            // Validate input
            const errors = securityService.validateInput(foodParameter, validateRules, { returnType: 'array' });
            if (errors.length > 0) {
                resultData.message = errors.map(s => s.message).join(', ');
                return res.json(resultData);
            }
            
            const isCreate = !req.body.id;
            let responseData;
            let foodId;
            
            if (isCreate) {
                // Thêm mới food_info
                responseData = await commonService.addRecordTable(foodParameter, 'food_info', true);
                if (responseData.success && responseData.data) {
                    foodId = responseData.data.insertId;
                    nutrientsParameter.id_food = foodId;
                    
                    // Thêm main_nutrients
                    await commonService.addRecordTable(nutrientsParameter, 'main_nutrients');
                    resultData.data = { id: foodId };
                }
            } else {
                foodId = req.body.id;
                delete foodParameter.created_by; // Không cập nhật created_by khi edit
                
                // Cập nhật food_info
                responseData = await commonService.updateRecordTable(foodParameter, { id: foodId }, 'food_info');
                if (responseData.success) {
                    // Cập nhật hoặc tạo main_nutrients
                    const existingNutrients = await commonService.getAllDataTable('main_nutrients', { id_food: foodId });
                    if (existingNutrients.success && existingNutrients.data && existingNutrients.data.length > 0) {
                        // Cập nhật
                        await commonService.updateRecordTable(nutrientsParameter, { id_food: foodId }, 'main_nutrients');
                    } else {
                        // Tạo mới
                        nutrientsParameter.id_food = foodId;
                        await commonService.addRecordTable(nutrientsParameter, 'main_nutrients');
                    }
                }
            }
            
            resultData.success = responseData.success;
            resultData.message = responseData.success 
                ? (isCreate ? 'Lưu thành công!' : 'Cập nhật thành công!')
                : responseData.message;
                
        } catch (error) {
            commonService.saveLog(req, error.message, error.stack)
            resultData.message = 'Đã xảy ra lỗi trong quá trình xử lý!';
        }
        
        res.json(resultData);
    },
    
    foodDelete: async (req, res) => {
        const resultData = {
            success: false,
            message: '',
            data: null,
            error: null
        };

        try {
            const { id } = req.params;
            const user = req.user;

            // Kiểm tra quyền truy cập
            if (!user.isAdmin) {
                throw new Error('Bạn không có quyền xóa danh sách này!');
            }

            // Kiểm tra ID
            if (!id) {
                throw new Error('Thiếu ID bản ghi!');
            }

            const recordId = parseInt(id, 10);
            if (isNaN(recordId)) {
                throw new Error('ID bản ghi không hợp lệ!');
            }

            // Xóa bản ghi từ food_info (hard delete vì không có trường active)
            const responseData = await commonService.deleteRecordTable({ id: recordId }, {}, 'food_info');
            
            if (!responseData || !responseData.success) {
                throw new Error('Không thể xóa bản ghi!');
            }
            
            // Xóa luôn main_nutrients liên quan
            await commonService.deleteRecordTable({ id_food: recordId }, {}, 'main_nutrients');

            resultData.success = responseData.success;
            resultData.message = 'Xóa thực phẩm thành công!';
            resultData.data = responseData.data || null;

            return res.status(200).json(resultData);

        } catch (error) {
            commonService.saveLog(req, error.message, error.stack)
            res.json(securityService.createErrorResponse(error.message || 'Đã xảy ra lỗi khi xử lý yêu cầu!', error, 500));
        }
    }
}

module.exports = adminService;
