// File test để kiểm tra user role
const userService = require('../services/userService');
const commonService = require('../services/commonService');

async function testUserRole() {
    console.log('=== Testing User Role ===');
    
    try {
        // Test 1: <PERSON><PERSON><PERSON> thông tin user từ database
        console.log('\n1. Testing getUserDetails...');
        const userDetails = await userService.getUserDetails(1); // Test với user ID = 1
        
        if (userDetails) {
            console.log('User details:', {
                id: userDetails.id,
                email: userDetails.email,
                fullname: userDetails.fullname,
                role_id: userDetails.role_id,
                isAdmin: userDetails.isAdmin
            });
        } else {
            console.log('User not found');
        }
        
        // Test 2: Kiểm tra role_id có phải array không
        console.log('\n2. Testing role_id validation...');
        if (userDetails && userDetails.role_id) {
            console.log('role_id is array:', Array.isArray(userDetails.role_id));
            console.log('role_id includes 1:', userDetails.role_id.includes(1));
            console.log('role_id includes 3:', userDetails.role_id.includes(3));
        }
        
        // Test 3: Test middleware logic
        console.log('\n3. Testing middleware logic...');
        const mockReq = {
            user: userDetails
        };
        
        if (mockReq.user && mockReq.user.role_id && Array.isArray(mockReq.user.role_id)) {
            console.log('User has valid role_id');
            
            // Test switch logic
            switch(true){
                case mockReq.user.role_id.includes(1):
                    console.log('User is admin - redirect to index');
                    break;
                case mockReq.user.role_id.includes(3):
                    console.log('User has role 3 - redirect to viem-gan');
                    break;
                case mockReq.user.role_id.includes(4):
                    console.log('User has role 4 - redirect to uon-van');
                    break;
                case mockReq.user.role_id.includes(5):
                    console.log('User has role 5 - redirect to hoi-chan');
                    break;
                case mockReq.user.role_id.includes(6):
                    console.log('User has role 6 - redirect to viem-gan-mt1');
                    break;
                case mockReq.user.role_id.includes(7):
                    console.log('User has role 7 - redirect to research');
                    break;
                case mockReq.user.role_id.includes(8):
                    console.log('User has role 8 - redirect to standard');
                    break;
                default:
                    console.log('User has no specific role - redirect to home');
            }
        } else {
            console.log('User does not have valid role_id');
        }
        
    } catch (error) {
        console.error('Error testing user role:', error);
    }
    
    console.log('\n=== User Role Test Completed ===');
}

// Chạy test
testUserRole().catch(console.error); 