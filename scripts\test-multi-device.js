const axios = require('axios');

// <PERSON><PERSON><PERSON> hình
const BASE_URL = 'http://localhost:3000';
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'password123';

// Test data
let authToken = null;
let devices = [];

async function testMultiDevice() {
    console.log('🧪 Bắt đầu test tính năng Multi-Device Login...\n');

    try {
        // 1. Đăng nhập lần đầu
        console.log('1️⃣ Đăng nhập lần đầu...');
        const loginResponse = await axios.post(`${BASE_URL}/login`, {
            email: TEST_EMAIL,
            password: TEST_PASSWORD,
            token: 'test-token' // Giả lập reCAPTCHA token
        });

        if (loginResponse.data.success) {
            console.log('✅ Đăng nhập thành công');
            authToken = loginResponse.headers['set-cookie']?.[0]?.split(';')[0].split('=')[1];
        } else {
            console.log('❌ Đăng nhập thất bại:', loginResponse.data.message);
            return;
        }

        // 2. Lấy danh sách thiết bị
        console.log('\n2️⃣ Lấy danh sách thiết bị...');
        const devicesResponse = await axios.get(`${BASE_URL}/devices`, {
            headers: {
                Cookie: `token=${authToken}`
            }
        });

        if (devicesResponse.data.success) {
            devices = devicesResponse.data.data.devices;
            console.log(`✅ Tìm thấy ${devices.length} thiết bị đang hoạt động`);
            devices.forEach((device, index) => {
                console.log(`   ${index + 1}. ${device.deviceName} (${device.deviceType}) - ${device.isCurrentSession ? 'Hiện tại' : 'Hoạt động'}`);
            });
        } else {
            console.log('❌ Không thể lấy danh sách thiết bị');
        }

        // 3. Lấy cài đặt session
        console.log('\n3️⃣ Lấy cài đặt session...');
        const settingsResponse = await axios.get(`${BASE_URL}/devices/settings`, {
            headers: {
                Cookie: `token=${authToken}`
            }
        });

        if (settingsResponse.data.success) {
            const settings = settingsResponse.data.data;
            console.log('✅ Cài đặt session:');
            console.log(`   - Số session tối đa: ${settings.max_sessions}`);
            console.log(`   - Timeout: ${settings.session_timeout_hours} giờ`);
            console.log(`   - Cho phép nhiều thiết bị: ${settings.allow_multiple_devices ? 'Có' : 'Không'}`);
        } else {
            console.log('❌ Không thể lấy cài đặt session');
        }

        // 4. Cập nhật cài đặt session
        console.log('\n4️⃣ Cập nhật cài đặt session...');
        const updateSettingsResponse = await axios.post(`${BASE_URL}/devices/settings`, {
            max_sessions: 3,
            session_timeout_hours: 12,
            allow_multiple_devices: 1
        }, {
            headers: {
                Cookie: `token=${authToken}`
            }
        });

        if (updateSettingsResponse.data.success) {
            console.log('✅ Cập nhật cài đặt thành công');
        } else {
            console.log('❌ Không thể cập nhật cài đặt');
        }

        // 5. Test logout thiết bị khác (nếu có)
        if (devices.length > 1) {
            const otherDevice = devices.find(d => !d.isCurrentSession);
            if (otherDevice) {
                console.log(`\n5️⃣ Logout thiết bị: ${otherDevice.deviceName}...`);
                const logoutResponse = await axios.post(`${BASE_URL}/devices/logout`, {
                    tokenId: otherDevice.tokenId
                }, {
                    headers: {
                        Cookie: `token=${authToken}`
                    }
                });

                if (logoutResponse.data.success) {
                    console.log('✅ Logout thiết bị thành công');
                } else {
                    console.log('❌ Không thể logout thiết bị');
                }
            }
        }

        // 6. Test logout tất cả thiết bị khác
        console.log('\n6️⃣ Test logout tất cả thiết bị khác...');
        const logoutAllResponse = await axios.post(`${BASE_URL}/devices/logout-all-others`, {}, {
            headers: {
                Cookie: `token=${authToken}`
            }
        });

        if (logoutAllResponse.data.success) {
            console.log('✅ Logout tất cả thiết bị khác thành công');
        } else {
            console.log('❌ Không thể logout tất cả thiết bị khác');
        }

        console.log('\n🎉 Test hoàn thành!');

    } catch (error) {
        console.error('❌ Lỗi trong quá trình test:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
        }
    }
}

// Chạy test
if (require.main === module) {
    testMultiDevice();
}

module.exports = { testMultiDevice }; 