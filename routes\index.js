var express       = require('express');
var router        = express.Router();

const commonService = require("../services/commonService");
const securityService = require("../services/securityService");
const foodService = require("../services/foodService");
const home          = require('../controllers/homeController');
const user          = require('../controllers/userController');
const device        = require('../controllers/deviceController');
const hepatitis     = require('../controllers/hepatitisController');
const hepatitisMt1  = require('../controllers/hepstitisMt1Controller');
const patient       = require('../controllers/patientController');
const tetanus       = require('../controllers/tetanusController');
const liverSurgery  = require('../controllers/liverSurgeryController');
const foodRation    = require('../controllers/foodRationController');
const research      = require('../controllers/researchController');
const standard      = require('../controllers/standardController');
const dishController = require('../controllers/dishController');

// Index
router.get("/", commonService.isAuthenticated, home.index);
router.post("/chat", home.chat);
router.post("/chat-open-route", home.chatopenroute);

// Device Management
router.get("/devices", commonService.isAuthenticated, device.getActiveDevices);
router.get("/devices-page", commonService.isAuthenticated, (req, res) => {
    res.render('devices', { user: req.user, errors: [] });
});
router.post("/devices/logout", commonService.isAuthenticatedPost, device.logoutDevice);
router.post("/devices/logout-all-others", commonService.isAuthenticatedPost, device.logoutAllOtherDevices);
router.get("/devices/settings", commonService.isAuthenticated, device.getSessionSettings);
router.post("/devices/settings", commonService.isAuthenticatedPost, device.updateSessionSettings);

// Viêm Gan - with proper authorization
router.get("/viem-gan", commonService.isAuthenticated, securityService.requirePermission('viem-gan', 'read'), patient.getlist);
router.get("/uon-van", commonService.isAuthenticated, securityService.requirePermission('uon-van', 'read'), patient.getlist);
router.get("/hoi-chan", commonService.isAuthenticated, securityService.requirePermission('hoi-chan', 'read'), patient.getlist);
router.get("/viem-gan-mt1", commonService.isAuthenticated, securityService.requirePermission('viem-gan-mt1', 'read'), patient.getlist);
router.get("/standard", commonService.isAuthenticated, securityService.requirePermission('standard', 'read'), patient.getlist);

router.get("/khau-phan-an/detail/:patient_id", commonService.isAuthenticated, securityService.requirePermission('khau-phan-an', 'read'), foodRation.index);
router.get("/khau-phan-an/food-name", commonService.isAuthenticated, securityService.requirePermission('khau-phan-an', 'read'), foodRation.foodName);
// API chung cho việc lấy thực phẩm với filter và search
router.get("/api/food-search", commonService.isAuthenticated, async (req, res) => {
    const type = req.query.type;
    const type_year = req.query.type_year;
    const search = req.query.search;
    const result = await foodService.getFoodForSelect(type, type_year, search);
    res.json(result);
});

// API lấy danh sách món ăn cho client
router.get("/api/dishes-for-select", commonService.isAuthenticated, dishController.getDishesForSelect);
router.get("/api/dish-foods/:id", commonService.isAuthenticated, dishController.getDishFoods);
router.post("/khau-phan-an/save-menu", commonService.isAuthenticatedPost, foodRation.saveMenu);
router.post("/khau-phan-an/save-table-config", commonService.isAuthenticatedPost, patient.saveTableDisplayConfig);
router.get("/khau-phan-an/get-table-config", commonService.isAuthenticated, patient.getTableDisplayConfig);

router.get("/patient/add/:path", commonService.isAuthenticated, patient.getCreate);
router.get("/patient/edit/:path/:id", commonService.isAuthenticated, patient.getEdit)
router.post("/patient/add", commonService.isAuthenticatedPost, patient.create);
router.post("/patient/update", commonService.isAuthenticatedPost, patient.update);
router.post("/patient/list", commonService.isAuthenticatedPostList, patient.list);
router.post("/patient/active", commonService.isAuthenticatedPost, patient.active);
router.get("/patient/detail/:path/:id", commonService.isAuthenticated, patient.detail);

// Viêm gan
router.get('/viem-gan/:patient_id/:type', commonService.isAuthenticated, hepatitis.index);
router.post("/viem-gan-create/:patient_id/:type", commonService.isAuthenticatedPost, hepatitis.createHepatitis);
router.post("/viem-gan-update/:patient_id/:type", commonService.isAuthenticatedPost, hepatitis.editHepatitis);
router.post("/viem-gan-list/:patient_id/:type", commonService.isAuthenticatedPostList, hepatitis.getListTable);

router.post('/viem-gan/get-broading/:id/:type', commonService.isAuthenticatedPost, hepatitis.dataBroading);
router.post('/viem-gan/add-broading/:patient_id/:type', commonService.isAuthenticatedPost, hepatitis.addBroading);
router.post('/viem-gan/update-broading/:type', commonService.isAuthenticatedPost, hepatitis.updateBroading);
router.post("/viem-gan/delete/broading/:id/:type", commonService.isAuthenticatedPost, hepatitis.deleteBroading);

router.post('/viem-gan/add-time/:patient_id/:type', commonService.isAuthenticatedPost, hepatitis.addTimes);
router.post('/viem-gan/update-time/:type', commonService.isAuthenticatedPost, hepatitis.updateTimes);
router.post("/viem-gan/delete/time/:id/:type", commonService.isAuthenticatedPost, hepatitis.deleteTime);
router.post("/viem-gan/data-time/:patient_id/:type", commonService.isAuthenticatedPost, hepatitis.dataTime);
// Uốn ván
router.get('/uon-van/:patient_id/:type', commonService.isAuthenticated, tetanus.index);
router.post("/uon-van-create/:patient_id/:type", commonService.isAuthenticatedPost, tetanus.createTetanus);
router.post("/uon-van-update/:patient_id/:type", commonService.isAuthenticatedPost, tetanus.editTetanus);
router.post("/uon-van-list/:patient_id/:type", commonService.isAuthenticatedPostList, tetanus.getListTable);

router.post('/uon-van/get-broading/:id/:type', commonService.isAuthenticatedPost, tetanus.dataBroading);
router.post('/uon-van/add-broading/:patient_id/:type', commonService.isAuthenticatedPost, tetanus.addBroading);
router.post('/uon-van/update-broading/:type', commonService.isAuthenticatedPost, tetanus.updateBroading);
router.post("/uon-van/delete/broading/:id/:type", commonService.isAuthenticatedPost, tetanus.deleteBroading);

router.post('/uon-van/add-time/:patient_id/:type', commonService.isAuthenticatedPost, tetanus.addTimes);
router.post('/uon-van/update-time/:type', commonService.isAuthenticatedPost, tetanus.updateTimes);
router.post("/uon-van/delete/time/:id/:type", commonService.isAuthenticatedPost, tetanus.deleteTime);
router.post("/uon-van/data-time/:patient_id/:type", commonService.isAuthenticatedPost, tetanus.dataTime);

// Cắt gan nhỏ
router.get('/hoi-chan/:patient_id/:type', commonService.isAuthenticated, liverSurgery.index);
router.post("/hoi-chan-list/:patient_id/:type", commonService.isAuthenticatedPostList, liverSurgery.getListTable);
router.post('/hoi-chan/get-broading/:id/:type', commonService.isAuthenticatedPost, liverSurgery.dataBroading);
router.post('/hoi-chan/add-broading/:patient_id/:type', commonService.isAuthenticatedPost, liverSurgery.addBroading);
router.post('/hoi-chan/update-broading/:type', commonService.isAuthenticatedPost, liverSurgery.updateBroading);
router.post("/hoi-chan/delete/broading/:id/:type", commonService.isAuthenticatedPost, liverSurgery.deleteBroading);

// Viêm gan Mt1
router.get('/viem-gan-mt1/:patient_id/:type', commonService.isAuthenticated, hepatitisMt1.index);
router.post("/viem-gan-mt1-create/:patient_id/:type", commonService.isAuthenticatedPost, hepatitisMt1.createHepatitis);
router.post("/viem-gan-mt1-update/:patient_id/:type", commonService.isAuthenticatedPost, hepatitisMt1.editHepatitis);

router.post('/viem-gan-mt1/add-time/:patient_id/:type', commonService.isAuthenticatedPost, hepatitisMt1.addTimes);
router.post('/viem-gan-mt1/update-time/:type', commonService.isAuthenticatedPost, hepatitisMt1.updateTimes);
router.post("/viem-gan-mt1/delete/time/:id/:type", commonService.isAuthenticatedPost, hepatitisMt1.deleteTime);
router.post("/viem-gan-mt1/data-time/:patient_id/:type", commonService.isAuthenticatedPost, hepatitisMt1.dataTime);

// Đánh giá khẩu phần ăn
//router.get('/khau-phan-an/:patient_id/:type', commonService.isAuthenticated, foodRation.index);

// Research
router.get('/research', commonService.isAuthenticated, research.getlist);
router.post("/research/list", commonService.isAuthenticatedPostList, research.getListTable);
router.post("/research/create", commonService.isAuthenticatedPost, research.add);
router.post("/research/update", commonService.isAuthenticatedPost, research.update);
router.post("/research/active", commonService.isAuthenticatedPost, research.active);
router.get('/research/detail/:id', commonService.isAuthenticated, research.detail);
router.post('/research/patient/list', commonService.isAuthenticatedPostList, research.patientList);
router.post("/research/patient/create", commonService.isAuthenticatedPost, research.patientAdd);
router.post("/research/patient/update", commonService.isAuthenticatedPost, research.patientUpdate);
router.post("/research/patient/active", commonService.isAuthenticatedPost, research.patientActive);
router.get("/research/export-excel/:research_id", commonService.isAuthenticated, research.exportExcel);
router.post("/research/export-excel/:research_id", commonService.isAuthenticatedPost, research.exportExcel);

// Phiếu hội chẩn
router.get('/standard/:patient_id/:type', commonService.isAuthenticated, standard.index);
router.post("/standard-create/:patient_id/:type", commonService.isAuthenticatedPost, standard.createStandard);
router.post("/standard-update/:patient_id/:type", commonService.isAuthenticatedPost, standard.editStandard);

router.post('/standard/add-time/:patient_id/:type', commonService.isAuthenticatedPost, standard.addTimes);
router.post('/standard/update-time/:type', commonService.isAuthenticatedPost, standard.updateTimes);
router.post("/standard/delete/time/:id/:type", commonService.isAuthenticatedPost, standard.deleteTime);
router.post("/standard/data-time/:patient_id/:type", commonService.isAuthenticatedPost, standard.dataTime);
router.get('/standard-download/:patient_id', commonService.isAuthenticated, standard.downloadStandard); 
router.get('/standard-download-template/:patient_id', commonService.isAuthenticated, standard.downloadStandardTemplate); 

// Đăng nhập
router.get("/login", user.getLogin);
router.get("/dang-ky", user.getSignUp);
router.post('/sign-up', user.signUp);
router.post('/login', user.login);
router.get('/logout', user.logout);
// Kiểm tra đăng nhập

// Thêm route test
router.get('/test-session', commonService.isAuthenticated, (req, res) => {
    res.json({
        user: req.user,
        authenticated: !!req.user,
        tokenId: req.user?.tokenId
    });
});

module.exports = router;
