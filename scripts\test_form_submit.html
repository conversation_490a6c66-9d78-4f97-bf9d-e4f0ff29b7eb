<!DOCTYPE html>
<html>
<head>
    <title>Test Form Submit</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Form Submit</h1>
    
    <form id="testForm">
        <input type="text" id="dishName" name="name" value="Test Mon An" placeholder="Tên món ăn">
        <input type="text" id="dishCategory" name="category" value="Test Category" placeholder="Loại món">
        <textarea id="dishDescription" name="description" placeholder="Mô tả">Test Description</textarea>
        <button type="button" onclick="testSubmit()">Test Submit</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        function testSubmit() {
            console.log('Testing form submit...');
            
            const dishFoods = [
                {
                    food_id: 1,
                    food_name: 'Test Food',
                    weight: 100,
                    food_energy: 200,
                    calculated_energy: 200
                }
            ];
            
            const dataToSend = {
                name: $('#dishName').val(),
                category: $('#dishCategory').val(),
                description: $('#dishDescription').val(),
                dish_foods: JSON.stringify(dishFoods)
            };
            
            console.log('Data to send:', dataToSend);
            
            $.ajax({
                url: '/admin/mon-an/upsert',
                type: 'POST',
                data: dataToSend,
                dataType: 'json',
                success: function(response) {
                    console.log('Success response:', response);
                    $('#result').html('<div style="color: green;">Success: ' + JSON.stringify(response) + '</div>');
                },
                error: function(xhr, status, error) {
                    console.error('Error response:', {
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });
                    $('#result').html('<div style="color: red;">Error: ' + error + '<br>Response: ' + xhr.responseText + '</div>');
                }
            });
        }
    </script>
</body>
</html> 