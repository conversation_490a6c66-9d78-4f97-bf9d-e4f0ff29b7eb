<div class="d-flex flex-wrap gap-3 justify-content-between mb-2 align-items-center px-2">
    <div class="d-flex gap-2">
        <label>Họ tên:</label>
        <div class="text-primary fw-bold patient-name" style="cursor: pointer;" 
             onclick="showPatientDetailModalWithData()" title="Click để xem thông tin chi tiết"><%=patient.fullname%></div>
    </div>
    <div class="d-flex gap-2">
        <label>Số điện thoại:</label>
        <div class="text-success"><%=patient.phone%></div>
    </div>
    <% if(path !== 'viem-gan-mt1'){ %>
    <div class="d-flex gap-2">
        <label>Chẩn đoán:</label>
        <div class="text-info"><%=patient.chuan_doan%></div>
    </div>
    <% } %>
    <% if(path == 'viem-gan-mt1'){ %>
        <div class="d-flex gap-2">
            <label>Điều tra viên:</label>
            <div class="text-info"><%=patient.dieu_tra_vien%></div>
        </div>
    <% } %>
    <div class="d-flex gap-2">
        <label>Phòng:</label>
        <div class="text-info"><%=patient.phong_dieu_tri%></div>
    </div>
    <div>
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" <%=patient.bien_ban == 1 ? 'checked' : ''%>  role="switch" onclick="discharged(this, '<%=patient.id%>', '<%=patient.fullname%>', 'bien_ban')">
            <label class="form-check-label">Biên bản</label>
        </div>
    </div>
    <div>
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" <%=patient.active == 2 ? 'checked disabled' : ''%>  role="switch" onclick="discharged(this, '<%=patient.id%>', '<%=patient.fullname%>', 'active')">
            <label class="form-check-label">Ra Viện</label>
        </div>
    </div>
</div>

<!-- Dữ liệu bệnh nhân -->
<script type="application/json" id="patient-data"><%- JSON.stringify(patient) %></script>

<script>
    // Hàm hiển thị modal với dữ liệu có sẵn
    function showPatientDetailModalWithData() {
        const patientData = JSON.parse(document.getElementById('patient-data').textContent);
        $('#patientDetailModal').modal('show');
        displayPatientDetail(patientData);
    }
</script>

<script>
    function discharged(el, id, name, type){
        event.preventDefault();
        confirmDialog('Xác nhận', 'Bạn có muốn chuyển trạng thái bệnh nhân ' + name + ' đã ra viện').then(responseData =>{
            if(responseData.isConfirmed && id){
                const pathname = window.location.pathname; 
                const parts = pathname.split('/'); // Tách chuỗi thành mảng
                const path = parts[1]; // Lấy phần tử cuối cùng
                var data = {id: id, path: path, active: 1, bien_ban: 0, type: type};
                switch (type) {
                    case 'bien_ban':
                        data.bien_ban = el.checked ? 0 : 1;
                        break;
                    case 'active':
                        data.active = el.checked ? 1 : 2;
                        break;
                    default: break;
                }
                $.ajax({
                    type: 'POST',
                    url: '/patient/active',
                    data: data,
                    beforeSend: function () {
                        loading.show();
                    },
                    success: function (result) {
                        loading.hide();
                        if (result.success) {
                            toarstMessage('Chuyển trạng thái thành công');
                            el.checked = !el.checked;
                            if(type == 'active'){
                                el.disabled = true;
                            }
                        } else {
                            toarstError(result.message);
                        }
                    },
                    error: function (jqXHR, exception) {
                        loading.hide();
                        ajax_call_error(jqXHR, exception);
                    }
                });
            }
        })
    }



</script>